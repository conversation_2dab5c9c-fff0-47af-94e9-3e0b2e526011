"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { 
  Eye, 
  Download, 
  FileText, 
  BarChart3, 
  Users, 
  Calendar,
  CheckCircle,
  AlertCircle,
  Clock,
  FileCheck,
  Printer,
  Mail,
  Maximize2,
  ZoomIn,
  ZoomOut
} from "lucide-react"
import { PhilippineReportType, ReportFormData, ReportPreview as ReportPreviewType } from "@/types"

interface ReportPreviewProps {
  reportData: ReportFormData
  preview?: ReportPreviewType
  isGenerating?: boolean
  onGenerate: () => void
  onClose: () => void
}

export function ReportPreview({ 
  reportData, 
  preview, 
  isGenerating = false, 
  onGenerate, 
  onClose 
}: ReportPreviewProps) {
  const [activeTab, setActiveTab] = useState("preview")
  const [zoomLevel, setZoomLevel] = useState(100)
  const [generationProgress, setGenerationProgress] = useState(0)

  useEffect(() => {
    if (isGenerating) {
      const interval = setInterval(() => {
        setGenerationProgress(prev => {
          if (prev >= 95) return prev
          return prev + Math.random() * 10
        })
      }, 500)
      return () => clearInterval(interval)
    }
  }, [isGenerating])

  const getReportTypeIcon = (type: PhilippineReportType) => {
    switch (type) {
      case 'SF2':
      case 'SF4':
        return FileCheck
      case 'custom_attendance':
        return BarChart3
      case 'sms_notifications':
        return Mail
      case 'deped_compliance':
        return CheckCircle
      default:
        return FileText
    }
  }

  const getReportTypeName = (type: PhilippineReportType) => {
    switch (type) {
      case 'SF2':
        return 'SF2 - Daily Attendance Report'
      case 'SF4':
        return 'SF4 - School Register'
      case 'custom_attendance':
        return 'Custom Attendance Report'
      case 'sms_notifications':
        return 'SMS Notifications Report'
      case 'deped_compliance':
        return 'DepEd Compliance Report'
      default:
        return 'Report'
    }
  }

  const mockPreviewData = {
    pageCount: 3,
    dataRows: 150,
    estimatedFileSize: 2.4,
    generationTime: 45
  }

  const previewData = preview?.previewData || mockPreviewData

  const ReportIcon = getReportTypeIcon(reportData.reportType)

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                <ReportIcon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  Report Preview
                </CardTitle>
                <CardDescription>
                  {getReportTypeName(reportData.reportType)} - {reportData.name}
                </CardDescription>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                {new Date(reportData.parameters.dateRange.start).toLocaleDateString()} - {new Date(reportData.parameters.dateRange.end).toLocaleDateString()}
              </Badge>
              <Badge variant="secondary">
                {reportData.parameters.format.toUpperCase()}
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="preview">Preview</TabsTrigger>
              <TabsTrigger value="details">Details</TabsTrigger>
              <TabsTrigger value="compliance">Compliance</TabsTrigger>
            </TabsList>

            <TabsContent value="preview" className="space-y-4">
              {isGenerating ? (
                <div className="space-y-4">
                  <div className="flex items-center justify-center p-8 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg">
                    <div className="text-center space-y-4">
                      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto" />
                      <div>
                        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                          Generating Report Preview
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          Please wait while we prepare your report...
                        </p>
                      </div>
                      <div className="w-full max-w-xs mx-auto">
                        <Progress value={generationProgress} className="w-full" />
                        <p className="text-xs text-gray-500 mt-1 text-center">
                          {Math.round(generationProgress)}% complete
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm" onClick={() => setZoomLevel(Math.max(50, zoomLevel - 25))}>
                        <ZoomOut className="h-4 w-4" />
                      </Button>
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        {zoomLevel}%
                      </span>
                      <Button variant="outline" size="sm" onClick={() => setZoomLevel(Math.min(200, zoomLevel + 25))}>
                        <ZoomIn className="h-4 w-4" />
                      </Button>
                    </div>
                    <Button variant="outline" size="sm">
                      <Maximize2 className="h-4 w-4 mr-2" />
                      Full Screen
                    </Button>
                  </div>

                  <div className="border rounded-lg overflow-hidden bg-white dark:bg-gray-900">
                    <div 
                      className="p-4 space-y-4 transition-transform origin-top-left"
                      style={{ transform: `scale(${zoomLevel / 100})` }}
                    >
                      {/* Mock Report Preview */}
                      <div className="space-y-4">
                        {/* Header */}
                        <div className="text-center border-b pb-4">
                          <h1 className="text-xl font-bold text-gray-900">
                            {getReportTypeName(reportData.reportType)}
                          </h1>
                          <p className="text-sm text-gray-600">
                            Tanauan School of Arts and Trade
                          </p>
                          <p className="text-xs text-gray-500">
                            Generated on {new Date().toLocaleDateString()}
                          </p>
                        </div>

                        {/* Report Content Preview */}
                        <div className="space-y-3">
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <strong>Report Period:</strong> {new Date(reportData.parameters.dateRange.start).toLocaleDateString()} - {new Date(reportData.parameters.dateRange.end).toLocaleDateString()}
                            </div>
                            <div>
                              <strong>Generated By:</strong> Admin User
                            </div>
                          </div>

                          {/* Sample Data Table */}
                          <div className="border rounded">
                            <table className="w-full text-xs">
                              <thead className="bg-gray-50 dark:bg-gray-800">
                                <tr>
                                  {reportData.reportType === 'SF2' && (
                                    <>
                                      <th className="p-2 text-left">Date</th>
                                      <th className="p-2 text-left">Male Present</th>
                                      <th className="p-2 text-left">Female Present</th>
                                      <th className="p-2 text-left">Total</th>
                                    </>
                                  )}
                                  {reportData.reportType === 'SF4' && (
                                    <>
                                      <th className="p-2 text-left">LRN</th>
                                      <th className="p-2 text-left">Last Name</th>
                                      <th className="p-2 text-left">First Name</th>
                                      <th className="p-2 text-left">Sex</th>
                                    </>
                                  )}
                                  {reportData.reportType === 'custom_attendance' && (
                                    <>
                                      <th className="p-2 text-left">Student</th>
                                      <th className="p-2 text-left">Grade</th>
                                      <th className="p-2 text-left">Status</th>
                                      <th className="p-2 text-left">Time</th>
                                    </>
                                  )}
                                </tr>
                              </thead>
                              <tbody>
                                {[1, 2, 3, 4, 5].map((row) => (
                                  <tr key={row} className="border-t">
                                    {reportData.reportType === 'SF2' && (
                                      <>
                                        <td className="p-2">Dec {row}, 2024</td>
                                        <td className="p-2">{15 + row}</td>
                                        <td className="p-2">{18 + row}</td>
                                        <td className="p-2">{33 + row * 2}</td>
                                      </>
                                    )}
                                    {reportData.reportType === 'SF4' && (
                                      <>
                                        <td className="p-2">123456789{row}</td>
                                        <td className="p-2">Student {row}</td>
                                        <td className="p-2">Sample</td>
                                        <td className="p-2">{row % 2 === 0 ? 'M' : 'F'}</td>
                                      </>
                                    )}
                                    {reportData.reportType === 'custom_attendance' && (
                                      <>
                                        <td className="p-2">Student {row}</td>
                                        <td className="p-2">Grade {row}</td>
                                        <td className="p-2">Present</td>
                                        <td className="p-2">8:0{row} AM</td>
                                      </>
                                    )}
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                          </div>

                          {reportData.parameters.includeCharts && (
                            <div className="border rounded p-4">
                              <h3 className="text-sm font-medium mb-2">Attendance Chart</h3>
                              <div className="h-32 bg-gray-100 dark:bg-gray-800 rounded flex items-center justify-center">
                                <BarChart3 className="h-8 w-8 text-gray-400" />
                                <span className="ml-2 text-sm text-gray-500">Chart Preview</span>
                              </div>
                            </div>
                          )}

                          {reportData.parameters.includeSignatures && (
                            <div className="grid grid-cols-2 gap-8 mt-8 pt-4 border-t">
                              <div className="text-center">
                                <div className="border-t border-gray-400 pt-1 text-xs">
                                  Class Adviser
                                </div>
                              </div>
                              <div className="text-center">
                                <div className="border-t border-gray-400 pt-1 text-xs">
                                  School Principal
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="details" className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      Report Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="grid gap-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Pages:</span>
                        <span className="font-medium">{previewData.pageCount}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Data Rows:</span>
                        <span className="font-medium">{previewData.dataRows.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">File Size:</span>
                        <span className="font-medium">{previewData.estimatedFileSize} MB</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Generation Time:</span>
                        <span className="font-medium">{previewData.generationTime}s</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base flex items-center gap-2">
                      <Users className="h-4 w-4" />
                      Filters Applied
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="space-y-2 text-sm">
                      <div>
                        <span className="text-gray-600 dark:text-gray-400">Grades:</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {reportData.parameters.filters.grades.length > 0 ? (
                            reportData.parameters.filters.grades.map((grade) => (
                              <Badge key={grade} variant="secondary" className="text-xs">
                                {grade}
                              </Badge>
                            ))
                          ) : (
                            <span className="text-gray-500">All Grades</span>
                          )}
                        </div>
                      </div>
                      <div>
                        <span className="text-gray-600 dark:text-gray-400">Sections:</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {reportData.parameters.filters.sections.length > 0 ? (
                            reportData.parameters.filters.sections.map((section) => (
                              <Badge key={section} variant="secondary" className="text-xs">
                                {section}
                              </Badge>
                            ))
                          ) : (
                            <span className="text-gray-500">All Sections</span>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-base">Report Options</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-3 md:grid-cols-3">
                    <div className="flex items-center gap-2">
                      {reportData.parameters.includeCharts ? (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      ) : (
                        <AlertCircle className="h-4 w-4 text-gray-400" />
                      )}
                      <span className="text-sm">Include Charts</span>
                    </div>
                    <div className="flex items-center gap-2">
                      {reportData.parameters.includeStatistics ? (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      ) : (
                        <AlertCircle className="h-4 w-4 text-gray-400" />
                      )}
                      <span className="text-sm">Include Statistics</span>
                    </div>
                    <div className="flex items-center gap-2">
                      {reportData.parameters.includeSignatures ? (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      ) : (
                        <AlertCircle className="h-4 w-4 text-gray-400" />
                      )}
                      <span className="text-sm">Digital Signatures</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="compliance" className="space-y-4">
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-base flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    DepEd Compliance Check
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {[
                      { rule: "Format Compliance", status: "passed", message: "Report format meets DepEd standards" },
                      { rule: "Required Fields", status: "passed", message: "All mandatory fields are present" },
                      { rule: "Data Validation", status: "passed", message: "Data integrity checks passed" },
                      { rule: "Signature Requirements", status: reportData.parameters.includeSignatures ? "passed" : "warning", message: reportData.parameters.includeSignatures ? "Digital signatures enabled" : "Digital signatures not enabled" }
                    ].map((check, index) => (
                      <div key={index} className="flex items-center gap-3 p-3 border rounded-lg">
                        {check.status === "passed" ? (
                          <CheckCircle className="h-5 w-5 text-green-600" />
                        ) : (
                          <AlertCircle className="h-5 w-5 text-yellow-600" />
                        )}
                        <div className="flex-1">
                          <div className="font-medium text-sm">{check.rule}</div>
                          <div className="text-xs text-gray-600 dark:text-gray-400">{check.message}</div>
                        </div>
                        <Badge variant={check.status === "passed" ? "default" : "secondary"}>
                          {check.status}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          <div className="flex justify-between pt-6 border-t">
            <Button variant="outline" onClick={onClose}>
              Close Preview
            </Button>
            <div className="flex gap-2">
              {reportData.delivery?.email?.enabled && (
                <Button variant="outline" size="sm">
                  <Mail className="h-4 w-4 mr-2" />
                  Email
                </Button>
              )}
              {reportData.delivery?.print?.enabled && (
                <Button variant="outline" size="sm">
                  <Printer className="h-4 w-4 mr-2" />
                  Print
                </Button>
              )}
              <Button onClick={onGenerate} disabled={isGenerating}>
                {isGenerating ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    Generating...
                  </>
                ) : (
                  <>
                    <Download className="h-4 w-4 mr-2" />
                    Generate Report
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
