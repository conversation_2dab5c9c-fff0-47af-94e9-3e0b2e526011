'use client'

import { useState, useEffect, useMemo } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Checkbox } from '@/components/ui/checkbox'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { LoadingSpinner, LoadingTable } from '@/components/ui/loading'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Search,
  Plus,
  Filter,
  Download,
  Upload,
  MoreHorizontal,
  Users,
  UserCheck,
  UserX,
  UserPlus,
  QrCode,
  Mail,
  Edit,
  Trash2,
  Eye,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight
} from 'lucide-react'
import { StudentStats, Student, StudentStatus, StudentFilters, StudentSortConfig, EnhancedStudent, StudentFormData } from '@/types'
import { StudentFormModal } from '@/components/students/student-form-modal'
import { StudentProfileModal } from '@/components/students/student-profile-modal'
import { BulkActionsModal } from '@/components/students/bulk-actions-modal'
import { ImportExportModal } from '@/components/students/import-export-modal'
import { DeleteConfirmationDialog } from '@/components/students/delete-confirmation-dialog'

// Mock data for demonstration
const mockStudents: Student[] = [
  {
    id: '1',
    studentId: 'TSAT-2024-001',
    firstName: 'John',
    lastName: 'Doe',
    middleName: 'Smith',
    email: '<EMAIL>',
    phone: '+63 ************',
    dateOfBirth: new Date('2005-03-15'),
    address: '123 Main St, Tanauan City',
    course: 'Information Technology',
    yearLevel: 2,
    section: 'IT-2A',
    status: 'active',
    qrCode: 'QR001',
    avatar: '/avatars/john-doe.jpg',
    guardianName: 'Jane Doe',
    guardianPhone: '+63 ************',
    enrollmentDate: new Date('2023-08-15'),
    createdAt: new Date('2023-08-15'),
    updatedAt: new Date('2024-01-15')
  },
  {
    id: '2',
    studentId: 'TSAT-2024-002',
    firstName: 'Maria',
    lastName: 'Santos',
    email: '<EMAIL>',
    phone: '+63 ************',
    dateOfBirth: new Date('2004-07-22'),
    address: '456 Oak Ave, Tanauan City',
    course: 'Business Administration',
    yearLevel: 3,
    section: 'BA-3B',
    status: 'active',
    qrCode: 'QR002',
    guardianName: 'Roberto Santos',
    guardianPhone: '+63 ************',
    enrollmentDate: new Date('2022-08-15'),
    createdAt: new Date('2022-08-15'),
    updatedAt: new Date('2024-01-10')
  },
  {
    id: '3',
    studentId: 'TSAT-2024-003',
    firstName: 'Carlos',
    lastName: 'Rodriguez',
    middleName: 'Miguel',
    email: '<EMAIL>',
    phone: '+63 ************',
    dateOfBirth: new Date('2005-11-08'),
    address: '789 Pine St, Tanauan City',
    course: 'Automotive Technology',
    yearLevel: 1,
    section: 'AT-1A',
    status: 'inactive',
    qrCode: 'QR003',
    guardianName: 'Elena Rodriguez',
    guardianPhone: '+63 ************',
    enrollmentDate: new Date('2024-08-15'),
    createdAt: new Date('2024-08-15'),
    updatedAt: new Date('2024-01-20')
  }
]

const mockStats: StudentStats = {
  total: 1250,
  active: 1180,
  inactive: 45,
  newEnrollments: 25,
  graduated: 320,
  dropped: 15
}

export default function StudentsPage() {
  const [students, setStudents] = useState<Student[]>(mockStudents)
  const [selectedStudents, setSelectedStudents] = useState<string[]>([])
  const [filters, setFilters] = useState<StudentFilters>({})
  const [sortConfig, setSortConfig] = useState<StudentSortConfig>({ field: 'lastName', direction: 'asc' })
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Modal states
  const [isAddModalOpen, setIsAddModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [isProfileModalOpen, setIsProfileModalOpen] = useState(false)
  const [isBulkActionsModalOpen, setIsBulkActionsModalOpen] = useState(false)
  const [isImportExportModalOpen, setIsImportExportModalOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null)
  const [selectedStudentProfile, setSelectedStudentProfile] = useState<EnhancedStudent | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)

  // Filter and sort students
  const filteredAndSortedStudents = useMemo(() => {
    let filtered = students

    // Apply filters
    if (filters.search) {
      const searchLower = filters.search.toLowerCase()
      filtered = filtered.filter(student =>
        student.firstName.toLowerCase().includes(searchLower) ||
        student.lastName.toLowerCase().includes(searchLower) ||
        student.studentId.toLowerCase().includes(searchLower) ||
        student.email?.toLowerCase().includes(searchLower)
      )
    }

    if (filters.status) {
      filtered = filtered.filter(student => student.status === filters.status)
    }

    if (filters.course) {
      filtered = filtered.filter(student => student.course === filters.course)
    }

    if (filters.yearLevel) {
      filtered = filtered.filter(student => student.yearLevel === filters.yearLevel)
    }

    // Apply sorting
    filtered.sort((a, b) => {
      const aValue = a[sortConfig.field]
      const bValue = b[sortConfig.field]

      if (aValue < bValue) return sortConfig.direction === 'asc' ? -1 : 1
      if (aValue > bValue) return sortConfig.direction === 'asc' ? 1 : -1
      return 0
    })

    return filtered
  }, [students, filters, sortConfig])

  // Pagination
  const paginatedStudents = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize
    return filteredAndSortedStudents.slice(startIndex, startIndex + pageSize)
  }, [filteredAndSortedStudents, currentPage, pageSize])

  const totalPages = Math.ceil(filteredAndSortedStudents.length / pageSize)

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedStudents(paginatedStudents.map(student => student.id))
    } else {
      setSelectedStudents([])
    }
  }

  const handleSelectStudent = (studentId: string, checked: boolean) => {
    if (checked) {
      setSelectedStudents(prev => [...prev, studentId])
    } else {
      setSelectedStudents(prev => prev.filter(id => id !== studentId))
    }
  }

  const getStatusBadge = (status: StudentStatus) => {
    const variants = {
      active: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      inactive: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',
      graduated: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
      dropped: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
    }

    return (
      <Badge className={variants[status]}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    )
  }

  const handleAddStudent = async (data: StudentFormData) => {
    try {
      setError(null)
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      const newStudent: Student = {
        id: Date.now().toString(),
        ...data,
        dateOfBirth: new Date(data.dateOfBirth),
        status: 'active',
        qrCode: `QR${Date.now()}`,
        enrollmentDate: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      }

      setStudents(prev => [...prev, newStudent])
    } catch (error) {
      setError('Failed to add student. Please try again.')
      throw error
    }
  }

  const handleEditStudent = async (data: StudentFormData) => {
    if (!selectedStudent) return

    try {
      setError(null)
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      const updatedStudent: Student = {
        ...selectedStudent,
        ...data,
        dateOfBirth: new Date(data.dateOfBirth),
        updatedAt: new Date()
      }

      setStudents(prev => prev.map(s => s.id === selectedStudent.id ? updatedStudent : s))
      setSelectedStudent(null)
    } catch (error) {
      setError('Failed to update student. Please try again.')
      throw error
    }
  }

  const handleViewProfile = (student: Student) => {
    // Convert Student to EnhancedStudent for profile view
    const enhancedStudent: EnhancedStudent = {
      ...student,
      fullName: `${student.firstName} ${student.middleName ? student.middleName + ' ' : ''}${student.lastName}`,
      guardians: student.guardianName ? [{
        name: student.guardianName,
        relationship: 'Parent/Guardian',
        phone: student.guardianPhone || '',
        emergencyContact: true
      }] : [],
      academicRecords: [],
      attendanceSummary: {
        totalDays: 100,
        presentDays: 85,
        absentDays: 10,
        lateDays: 5,
        excusedDays: 0,
        attendanceRate: 85
      },
      smsNotifications: [],
      qrCodeData: {
        studentId: student.id,
        generatedAt: student.createdAt,
        isActive: true,
        scanCount: 0
      },
      emergencyContacts: []
    }

    setSelectedStudentProfile(enhancedStudent)
    setIsProfileModalOpen(true)
  }

  const handleEditClick = (student: Student) => {
    setSelectedStudent(student)
    setIsEditModalOpen(true)
  }

  const handleDeleteClick = (student: Student) => {
    setSelectedStudent(student)
    setIsDeleteDialogOpen(true)
  }

  const handleDeleteConfirm = async (studentId: string) => {
    try {
      setIsDeleting(true)
      setError(null)

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Optimistic update
      setStudents(prev => prev.filter(s => s.id !== studentId))
      setSelectedStudents(prev => prev.filter(id => id !== studentId))

    } catch (error) {
      setError('Failed to delete student. Please try again.')
      throw error
    } finally {
      setIsDeleting(false)
    }
  }

  return (
    <MainLayout title="Students">
      <div className="space-y-6">
        {/* Header Actions */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              Student Management
            </h2>
            <p className="text-gray-600 dark:text-gray-400">
              Manage student records, attendance, and academic information
            </p>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsImportExportModalOpen(true)}
            >
              <Upload className="h-4 w-4 mr-2" />
              Import/Export
            </Button>
            <Button size="sm" onClick={() => setIsAddModalOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Student
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Students</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockStats.total.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                +{mockStats.newEnrollments} from last month
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Students</CardTitle>
              <UserCheck className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockStats.active.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                {((mockStats.active / mockStats.total) * 100).toFixed(1)}% of total
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Inactive Students</CardTitle>
              <UserX className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockStats.inactive}</div>
              <p className="text-xs text-muted-foreground">
                {((mockStats.inactive / mockStats.total) * 100).toFixed(1)}% of total
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">New Enrollments</CardTitle>
              <UserPlus className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockStats.newEnrollments}</div>
              <p className="text-xs text-muted-foreground">
                This month
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardHeader>
            <CardTitle>Student Directory</CardTitle>
            <CardDescription>
              Search and filter student records
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col lg:flex-row gap-4 mb-6">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search students..."
                    value={filters.search || ''}
                    onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Select value={filters.status || 'all'} onValueChange={(value) => setFilters(prev => ({ ...prev, status: value === 'all' ? undefined : value as StudentStatus }))}>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                    <SelectItem value="graduated">Graduated</SelectItem>
                    <SelectItem value="dropped">Dropped</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={filters.course || 'all'} onValueChange={(value) => setFilters(prev => ({ ...prev, course: value === 'all' ? undefined : value }))}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Course" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Courses</SelectItem>
                    <SelectItem value="Information Technology">Information Technology</SelectItem>
                    <SelectItem value="Business Administration">Business Administration</SelectItem>
                    <SelectItem value="Automotive Technology">Automotive Technology</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={filters.yearLevel?.toString() || 'all'} onValueChange={(value) => setFilters(prev => ({ ...prev, yearLevel: value === 'all' ? undefined : parseInt(value) }))}>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="Year" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Years</SelectItem>
                    <SelectItem value="1">Year 1</SelectItem>
                    <SelectItem value="2">Year 2</SelectItem>
                    <SelectItem value="3">Year 3</SelectItem>
                    <SelectItem value="4">Year 4</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Bulk Actions */}
            {selectedStudents.length > 0 && (
              <div className="flex items-center gap-2 mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <span className="text-sm font-medium">
                  {selectedStudents.length} student{selectedStudents.length > 1 ? 's' : ''} selected
                </span>
                <div className="flex gap-2 ml-auto">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsBulkActionsModalOpen(true)}
                  >
                    <QrCode className="h-4 w-4 mr-2" />
                    Bulk Actions
                  </Button>
                </div>
              </div>
            )}

            {/* Error Display */}
            {error && (
              <Alert className="border-red-200 bg-red-50">
                <AlertCircle className="h-4 w-4 text-red-600" />
                <AlertDescription className="text-red-800">
                  {error}
                </AlertDescription>
              </Alert>
            )}

            {/* Students Table */}
            <div className="rounded-md border">
              {isLoading ? (
                <div className="p-6">
                  <LoadingTable rows={pageSize} columns={8} />
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12">
                        <Checkbox
                          checked={selectedStudents.length === paginatedStudents.length && paginatedStudents.length > 0}
                          onCheckedChange={handleSelectAll}
                        />
                      </TableHead>
                      <TableHead>Photo</TableHead>
                      <TableHead>Name</TableHead>
                      <TableHead>Student ID</TableHead>
                      <TableHead>Grade & Section</TableHead>
                      <TableHead>Contact</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {paginatedStudents.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                          No students found. Try adjusting your filters or add new students.
                        </TableCell>
                      </TableRow>
                    ) : (
                      paginatedStudents.map((student) => (
                    <TableRow key={student.id}>
                      <TableCell>
                        <Checkbox
                          checked={selectedStudents.includes(student.id)}
                          onCheckedChange={(checked) => handleSelectStudent(student.id, checked as boolean)}
                        />
                      </TableCell>
                      <TableCell>
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={student.avatar} alt={`${student.firstName} ${student.lastName}`} />
                          <AvatarFallback>
                            {student.firstName.charAt(0)}{student.lastName.charAt(0)}
                          </AvatarFallback>
                        </Avatar>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">
                          {student.firstName} {student.lastName}
                        </div>
                        {student.middleName && (
                          <div className="text-sm text-muted-foreground">
                            {student.middleName}
                          </div>
                        )}
                      </TableCell>
                      <TableCell className="font-mono text-sm">
                        {student.studentId}
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div className="font-medium">{student.course}</div>
                          <div className="text-muted-foreground">
                            Year {student.yearLevel} - {student.section}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {student.email && (
                            <div className="text-muted-foreground">{student.email}</div>
                          )}
                          {student.phone && (
                            <div className="text-muted-foreground">{student.phone}</div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(student.status)}
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">Open menu</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => handleViewProfile(student)}>
                              <Eye className="mr-2 h-4 w-4" />
                              View Profile
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleEditClick(student)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit Student
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <QrCode className="mr-2 h-4 w-4" />
                              Generate QR
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Mail className="mr-2 h-4 w-4" />
                              Send SMS
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              className="text-red-600"
                              onClick={() => handleDeleteClick(student)}
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete Student
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            )}
          </div>

            {/* Pagination */}
            <div className="flex items-center justify-between px-2 py-4">
              <div className="flex items-center space-x-2">
                <p className="text-sm font-medium">Rows per page</p>
                <Select
                  value={pageSize.toString()}
                  onValueChange={(value) => {
                    setPageSize(parseInt(value))
                    setCurrentPage(1)
                  }}
                >
                  <SelectTrigger className="h-8 w-[70px]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent side="top">
                    {[5, 10, 20, 30, 40, 50].map((size) => (
                      <SelectItem key={size} value={size.toString()}>
                        {size}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center space-x-6 lg:space-x-8">
                <div className="flex w-[100px] items-center justify-center text-sm font-medium">
                  Page {currentPage} of {totalPages}
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    className="hidden h-8 w-8 p-0 lg:flex"
                    onClick={() => setCurrentPage(1)}
                    disabled={currentPage === 1}
                  >
                    <span className="sr-only">Go to first page</span>
                    <ChevronsLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    className="h-8 w-8 p-0"
                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1}
                  >
                    <span className="sr-only">Go to previous page</span>
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    className="h-8 w-8 p-0"
                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                    disabled={currentPage === totalPages}
                  >
                    <span className="sr-only">Go to next page</span>
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    className="hidden h-8 w-8 p-0 lg:flex"
                    onClick={() => setCurrentPage(totalPages)}
                    disabled={currentPage === totalPages}
                  >
                    <span className="sr-only">Go to last page</span>
                    <ChevronsRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Modals */}
        <StudentFormModal
          open={isAddModalOpen}
          onOpenChange={setIsAddModalOpen}
          onSubmit={handleAddStudent}
        />

        <StudentFormModal
          open={isEditModalOpen}
          onOpenChange={setIsEditModalOpen}
          student={selectedStudent || undefined}
          onSubmit={handleEditStudent}
        />

        <StudentProfileModal
          open={isProfileModalOpen}
          onOpenChange={setIsProfileModalOpen}
          student={selectedStudentProfile}
        />

        <BulkActionsModal
          open={isBulkActionsModalOpen}
          onOpenChange={setIsBulkActionsModalOpen}
          selectedStudents={selectedStudents.map(id => students.find(s => s.id === id)!).filter(Boolean)}
          onComplete={() => {
            setSelectedStudents([])
            setIsBulkActionsModalOpen(false)
          }}
        />

        <ImportExportModal
          open={isImportExportModalOpen}
          onOpenChange={setIsImportExportModalOpen}
          students={students}
          onImportComplete={(newStudents) => {
            setStudents(prev => [...prev, ...newStudents])
            setIsImportExportModalOpen(false)
          }}
        />

        <DeleteConfirmationDialog
          open={isDeleteDialogOpen}
          onOpenChange={setIsDeleteDialogOpen}
          student={selectedStudent}
          onConfirm={handleDeleteConfirm}
          isDeleting={isDeleting}
        />
      </div>
    </MainLayout>
  )
}
