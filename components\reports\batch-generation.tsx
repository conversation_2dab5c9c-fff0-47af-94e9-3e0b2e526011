"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { 
  Play, 
  Pause, 
  Square, 
  Plus, 
  Trash2, 
  FileText,
  CheckCircle,
  Clock,
  XCircle,
  AlertCircle,
  Download,
  Eye,
  Settings,
  Users,
  Calendar,
  FileCheck,
  Shield,
  Signature,
  Key,
  Lock
} from "lucide-react"
import { BatchReportGeneration, PhilippineReportType, DigitalSignature } from "@/types"

interface BatchGenerationProps {
  batches: BatchReportGeneration[]
  onCreateBatch: () => void
  onStartBatch: (batchId: string) => void
  onPauseBatch: (batchId: string) => void
  onCancelBatch: (batchId: string) => void
  onDeleteBatch: (batchId: string) => void
  onViewResults: (batchId: string) => void
  isLoading?: boolean
}

// Mock data for demonstration
const mockBatches: BatchReportGeneration[] = [
  {
    id: "1",
    name: "Monthly SF2 Reports - All Grades",
    description: "Generate SF2 reports for all grade levels",
    reports: [
      { templateId: "sf2-grade1", parameters: {} as any, priority: 1 },
      { templateId: "sf2-grade2", parameters: {} as any, priority: 2 },
      { templateId: "sf2-grade3", parameters: {} as any, priority: 3 }
    ],
    status: "completed",
    progress: {
      total: 3,
      completed: 3,
      failed: 0
    },
    createdBy: "Admin User",
    createdAt: new Date("2024-12-10T09:00:00"),
    startedAt: new Date("2024-12-10T09:05:00"),
    completedAt: new Date("2024-12-10T09:15:00"),
    results: [
      { templateId: "sf2-grade1", reportId: "r1", status: "completed" },
      { templateId: "sf2-grade2", reportId: "r2", status: "completed" },
      { templateId: "sf2-grade3", reportId: "r3", status: "completed" }
    ]
  },
  {
    id: "2",
    name: "Weekly Attendance Summary",
    description: "Custom attendance reports for all sections",
    reports: [
      { templateId: "custom-attendance-1", parameters: {} as any, priority: 1 },
      { templateId: "custom-attendance-2", parameters: {} as any, priority: 2 }
    ],
    status: "running",
    progress: {
      total: 2,
      completed: 1,
      failed: 0,
      current: "custom-attendance-2"
    },
    createdBy: "Teacher Garcia",
    createdAt: new Date("2024-12-15T14:00:00"),
    startedAt: new Date("2024-12-15T14:05:00"),
    results: [
      { templateId: "custom-attendance-1", reportId: "r4", status: "completed" },
      { templateId: "custom-attendance-2", status: "running" }
    ]
  },
  {
    id: "3",
    name: "DepEd Compliance Reports",
    description: "Generate all required compliance reports",
    reports: [
      { templateId: "sf2-compliance", parameters: {} as any, priority: 1 },
      { templateId: "sf4-compliance", parameters: {} as any, priority: 2 }
    ],
    status: "pending",
    progress: {
      total: 2,
      completed: 0,
      failed: 0
    },
    createdBy: "Principal",
    createdAt: new Date("2024-12-15T16:00:00"),
    results: []
  }
]

const mockDigitalSignatures: DigitalSignature[] = [
  {
    id: "1",
    signerName: "Maria Santos",
    signerTitle: "School Principal",
    signedAt: new Date("2024-12-15T10:30:00"),
    certificateId: "CERT-001",
    isValid: true,
    verificationCode: "VS-2024-001"
  },
  {
    id: "2",
    signerName: "Juan Dela Cruz",
    signerTitle: "Class Adviser",
    signedAt: new Date("2024-12-15T11:15:00"),
    certificateId: "CERT-002",
    isValid: true,
    verificationCode: "VS-2024-002"
  }
]

export function BatchGeneration({ 
  batches = mockBatches,
  onCreateBatch,
  onStartBatch,
  onPauseBatch,
  onCancelBatch,
  onDeleteBatch,
  onViewResults,
  isLoading = false 
}: BatchGenerationProps) {
  const [activeTab, setActiveTab] = useState("batches")
  const [selectedBatches, setSelectedBatches] = useState<string[]>([])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case "running":
        return <Clock className="h-4 w-4 text-blue-600" />
      case "failed":
        return <XCircle className="h-4 w-4 text-red-600" />
      case "pending":
        return <AlertCircle className="h-4 w-4 text-yellow-600" />
      case "cancelled":
        return <XCircle className="h-4 w-4 text-gray-600" />
      default:
        return <AlertCircle className="h-4 w-4 text-gray-600" />
    }
  }

  const getStatusBadge = (status: string) => {
    const variants = {
      completed: "default",
      running: "secondary",
      failed: "destructive",
      pending: "outline",
      cancelled: "secondary"
    } as const

    return (
      <Badge variant={variants[status as keyof typeof variants] || "secondary"} className="flex items-center gap-1">
        {getStatusIcon(status)}
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    )
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date)
  }

  const calculateProgress = (batch: BatchReportGeneration) => {
    if (batch.progress.total === 0) return 0
    return Math.round((batch.progress.completed / batch.progress.total) * 100)
  }

  const handleSelectBatch = (batchId: string, checked: boolean) => {
    if (checked) {
      setSelectedBatches([...selectedBatches, batchId])
    } else {
      setSelectedBatches(selectedBatches.filter(id => id !== batchId))
    }
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedBatches(batches.map(b => b.id))
    } else {
      setSelectedBatches([])
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Batch Report Generation
              </CardTitle>
              <CardDescription>
                Generate multiple reports simultaneously with digital signature support
              </CardDescription>
            </div>
            <Button onClick={onCreateBatch}>
              <Plus className="h-4 w-4 mr-2" />
              New Batch
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList>
              <TabsTrigger value="batches">Batch Jobs</TabsTrigger>
              <TabsTrigger value="signatures">Digital Signatures</TabsTrigger>
              <TabsTrigger value="queue">Generation Queue</TabsTrigger>
            </TabsList>

            <TabsContent value="batches" className="space-y-4">
              {/* Bulk Actions */}
              {selectedBatches.length > 0 && (
                <div className="flex items-center gap-2 p-3 bg-blue-50 dark:bg-blue-950 rounded-lg">
                  <span className="text-sm text-blue-800 dark:text-blue-200">
                    {selectedBatches.length} batch(es) selected
                  </span>
                  <div className="flex gap-2 ml-auto">
                    <Button size="sm" variant="outline">
                      <Play className="h-4 w-4 mr-1" />
                      Start
                    </Button>
                    <Button size="sm" variant="outline">
                      <Pause className="h-4 w-4 mr-1" />
                      Pause
                    </Button>
                    <Button size="sm" variant="outline">
                      <Trash2 className="h-4 w-4 mr-1" />
                      Delete
                    </Button>
                  </div>
                </div>
              )}

              {/* Batch Table */}
              <div className="border rounded-lg">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12">
                        <Checkbox
                          checked={selectedBatches.length === batches.length && batches.length > 0}
                          onCheckedChange={handleSelectAll}
                        />
                      </TableHead>
                      <TableHead>Batch Name</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Progress</TableHead>
                      <TableHead>Reports</TableHead>
                      <TableHead>Created</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {isLoading ? (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-8">
                          <div className="flex items-center justify-center gap-2">
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600" />
                            Loading batches...
                          </div>
                        </TableCell>
                      </TableRow>
                    ) : batches.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-8 text-gray-500">
                          No batch jobs found
                        </TableCell>
                      </TableRow>
                    ) : (
                      batches.map((batch) => (
                        <TableRow key={batch.id}>
                          <TableCell>
                            <Checkbox
                              checked={selectedBatches.includes(batch.id)}
                              onCheckedChange={(checked) => handleSelectBatch(batch.id, checked as boolean)}
                            />
                          </TableCell>
                          <TableCell>
                            <div className="space-y-1">
                              <div className="font-medium text-sm">{batch.name}</div>
                              <div className="text-xs text-gray-500">{batch.description}</div>
                              <div className="flex items-center gap-2 text-xs text-gray-500">
                                <Users className="h-3 w-3" />
                                {batch.createdBy}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="space-y-1">
                              {getStatusBadge(batch.status)}
                              {batch.status === "running" && batch.progress.current && (
                                <div className="text-xs text-gray-500">
                                  Current: {batch.progress.current}
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="space-y-2">
                              <div className="flex items-center justify-between text-sm">
                                <span>{batch.progress.completed}/{batch.progress.total}</span>
                                <span>{calculateProgress(batch)}%</span>
                              </div>
                              <Progress value={calculateProgress(batch)} className="w-full" />
                              {batch.progress.failed > 0 && (
                                <div className="text-xs text-red-600">
                                  {batch.progress.failed} failed
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">
                              {batch.reports.length} reports
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              {formatDate(batch.createdAt)}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex gap-1">
                              {batch.status === "pending" && (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => onStartBatch(batch.id)}
                                >
                                  <Play className="h-3 w-3" />
                                </Button>
                              )}
                              {batch.status === "running" && (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => onPauseBatch(batch.id)}
                                >
                                  <Pause className="h-3 w-3" />
                                </Button>
                              )}
                              {batch.status === "completed" && (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => onViewResults(batch.id)}
                                >
                                  <Eye className="h-3 w-3" />
                                </Button>
                              )}
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => onDeleteBatch(batch.id)}
                                className="text-red-600"
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>

            <TabsContent value="signatures" className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base flex items-center gap-2">
                      <Signature className="h-4 w-4" />
                      Digital Signature Setup
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-3">
                      <div className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <Key className="h-5 w-5 text-blue-600" />
                          <div>
                            <div className="font-medium text-sm">Certificate Status</div>
                            <div className="text-xs text-gray-500">Digital certificate validation</div>
                          </div>
                        </div>
                        <Badge variant="default" className="flex items-center gap-1">
                          <CheckCircle className="h-3 w-3" />
                          Valid
                        </Badge>
                      </div>

                      <div className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <Lock className="h-5 w-5 text-green-600" />
                          <div>
                            <div className="font-medium text-sm">Signature Authority</div>
                            <div className="text-xs text-gray-500">Authorized signers</div>
                          </div>
                        </div>
                        <Badge variant="outline">
                          2 signers
                        </Badge>
                      </div>

                      <div className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <Shield className="h-5 w-5 text-orange-600" />
                          <div>
                            <div className="font-medium text-sm">Compliance Level</div>
                            <div className="text-xs text-gray-500">DepEd requirements</div>
                          </div>
                        </div>
                        <Badge variant="default">
                          Full Compliance
                        </Badge>
                      </div>
                    </div>

                    <Button className="w-full" variant="outline">
                      <Settings className="h-4 w-4 mr-2" />
                      Configure Signatures
                    </Button>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base">Recent Signatures</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {mockDigitalSignatures.map((signature) => (
                        <div key={signature.id} className="flex items-center justify-between p-3 border rounded-lg">
                          <div className="flex items-center gap-3">
                            <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                              <Signature className="h-4 w-4 text-blue-600" />
                            </div>
                            <div>
                              <div className="font-medium text-sm">{signature.signerName}</div>
                              <div className="text-xs text-gray-500">{signature.signerTitle}</div>
                              <div className="text-xs text-gray-500">
                                {formatDate(signature.signedAt)}
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <Badge variant={signature.isValid ? "default" : "destructive"} className="text-xs">
                              {signature.isValid ? "Valid" : "Invalid"}
                            </Badge>
                            <div className="text-xs text-gray-500 mt-1">
                              {signature.verificationCode}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="queue" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Generation Queue</CardTitle>
                  <CardDescription>
                    Real-time view of report generation queue and system status
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid gap-4 md:grid-cols-3">
                      <div className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm text-gray-600 dark:text-gray-400">Queue Length</p>
                            <p className="text-2xl font-bold">5</p>
                          </div>
                          <Clock className="h-8 w-8 text-blue-600" />
                        </div>
                      </div>

                      <div className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm text-gray-600 dark:text-gray-400">Processing</p>
                            <p className="text-2xl font-bold">2</p>
                          </div>
                          <Settings className="h-8 w-8 text-green-600 animate-spin" />
                        </div>
                      </div>

                      <div className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm text-gray-600 dark:text-gray-400">Avg. Time</p>
                            <p className="text-2xl font-bold">45s</p>
                          </div>
                          <Calendar className="h-8 w-8 text-purple-600" />
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <h4 className="font-medium">Current Queue</h4>
                      <div className="space-y-2">
                        {[
                          { name: "SF2 Grade 1A - December", status: "processing", progress: 75 },
                          { name: "SF4 School Register", status: "processing", progress: 30 },
                          { name: "Custom Attendance Report", status: "queued", progress: 0 },
                          { name: "SMS Delivery Report", status: "queued", progress: 0 },
                          { name: "DepEd Compliance Audit", status: "queued", progress: 0 }
                        ].map((item, index) => (
                          <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                            <div className="flex items-center gap-3">
                              <FileText className="h-4 w-4 text-gray-600" />
                              <div>
                                <div className="font-medium text-sm">{item.name}</div>
                                <div className="text-xs text-gray-500">
                                  {item.status === "processing" ? "Processing..." : "Waiting in queue"}
                                </div>
                              </div>
                            </div>
                            <div className="flex items-center gap-3">
                              {item.status === "processing" && (
                                <div className="w-20">
                                  <Progress value={item.progress} className="h-2" />
                                </div>
                              )}
                              <Badge variant={item.status === "processing" ? "default" : "outline"}>
                                {item.status}
                              </Badge>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
