"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { 
  Download, 
  Eye, 
  Share, 
  Trash2, 
  Search, 
  Filter,
  MoreHorizontal,
  FileText,
  Calendar,
  User,
  CheckCircle,
  Clock,
  AlertCircle,
  XCircle,
  Archive,
  Mail,
  Printer,
  FileCheck,
  BarChart3,
  MessageSquare,
  Shield
} from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { GeneratedReport, PhilippineReportType, ReportStatus } from "@/types"

interface GeneratedReportsTableProps {
  reports: GeneratedReport[]
  onDownload: (reportId: string) => void
  onPreview: (reportId: string) => void
  onShare: (reportId: string) => void
  onDelete: (reportId: string) => void
  onArchive: (reportId: string) => void
  isLoading?: boolean
}

// Mock data for demonstration
const mockReports: GeneratedReport[] = [
  {
    id: "1",
    name: "SF2 Daily Attendance - December 2024",
    type: "SF2",
    status: "completed",
    fileUrl: "/reports/sf2-dec-2024.pdf",
    fileSize: 2400000,
    generatedBy: "Admin User",
    generatedAt: new Date("2024-12-15T14:30:00"),
    downloadCount: 12,
    lastDownloaded: new Date("2024-12-15T16:45:00"),
    isArchived: false,
    complianceChecks: [],
    parameters: {} as any
  },
  {
    id: "2",
    name: "SF4 School Register - Grade 1A",
    type: "SF4",
    status: "completed",
    fileUrl: "/reports/sf4-grade1a.pdf",
    fileSize: 1800000,
    generatedBy: "Teacher Garcia",
    generatedAt: new Date("2024-12-14T10:15:00"),
    downloadCount: 8,
    lastDownloaded: new Date("2024-12-14T11:30:00"),
    isArchived: false,
    complianceChecks: [],
    parameters: {} as any
  },
  {
    id: "3",
    name: "Custom Attendance Report - BSIT",
    type: "custom_attendance",
    status: "generating",
    progress: 75,
    generatedBy: "Admin User",
    generatedAt: new Date("2024-12-15T15:00:00"),
    downloadCount: 0,
    isArchived: false,
    complianceChecks: [],
    parameters: {} as any
  },
  {
    id: "4",
    name: "SMS Notifications Report - November",
    type: "sms_notifications",
    status: "failed",
    error: "Network timeout during generation",
    generatedBy: "System Admin",
    generatedAt: new Date("2024-12-13T09:20:00"),
    downloadCount: 0,
    isArchived: false,
    complianceChecks: [],
    parameters: {} as any
  },
  {
    id: "5",
    name: "DepEd Compliance Audit - Q4 2024",
    type: "deped_compliance",
    status: "scheduled",
    generatedBy: "Principal",
    generatedAt: new Date("2024-12-20T08:00:00"),
    downloadCount: 0,
    isArchived: false,
    complianceChecks: [],
    parameters: {} as any
  }
]

export function GeneratedReportsTable({ 
  reports = mockReports, 
  onDownload, 
  onPreview, 
  onShare, 
  onDelete, 
  onArchive,
  isLoading = false 
}: GeneratedReportsTableProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<ReportStatus | "all">("all")
  const [typeFilter, setTypeFilter] = useState<PhilippineReportType | "all">("all")
  const [selectedReports, setSelectedReports] = useState<string[]>([])

  const filteredReports = reports.filter(report => {
    const matchesSearch = report.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         report.generatedBy.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || report.status === statusFilter
    const matchesType = typeFilter === "all" || report.type === typeFilter
    
    return matchesSearch && matchesStatus && matchesType
  })

  const getStatusIcon = (status: ReportStatus) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case "generating":
        return <Clock className="h-4 w-4 text-blue-600" />
      case "failed":
        return <XCircle className="h-4 w-4 text-red-600" />
      case "scheduled":
        return <Calendar className="h-4 w-4 text-purple-600" />
      case "pending":
        return <Clock className="h-4 w-4 text-yellow-600" />
      default:
        return <AlertCircle className="h-4 w-4 text-gray-600" />
    }
  }

  const getStatusBadge = (status: ReportStatus) => {
    const variants = {
      completed: "default",
      generating: "secondary",
      failed: "destructive",
      scheduled: "outline",
      pending: "secondary",
      delivered: "default"
    } as const

    return (
      <Badge variant={variants[status] || "secondary"} className="flex items-center gap-1">
        {getStatusIcon(status)}
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    )
  }

  const getTypeIcon = (type: PhilippineReportType) => {
    switch (type) {
      case "SF2":
      case "SF4":
        return <FileCheck className="h-4 w-4 text-blue-600" />
      case "custom_attendance":
        return <BarChart3 className="h-4 w-4 text-green-600" />
      case "sms_notifications":
        return <MessageSquare className="h-4 w-4 text-purple-600" />
      case "deped_compliance":
        return <Shield className="h-4 w-4 text-orange-600" />
      default:
        return <FileText className="h-4 w-4 text-gray-600" />
    }
  }

  const formatFileSize = (bytes: number) => {
    if (!bytes) return "N/A"
    const mb = bytes / (1024 * 1024)
    return `${mb.toFixed(1)} MB`
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date)
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedReports(filteredReports.map(r => r.id))
    } else {
      setSelectedReports([])
    }
  }

  const handleSelectReport = (reportId: string, checked: boolean) => {
    if (checked) {
      setSelectedReports([...selectedReports, reportId])
    } else {
      setSelectedReports(selectedReports.filter(id => id !== reportId))
    }
  }

  const handleBulkAction = (action: string) => {
    selectedReports.forEach(reportId => {
      switch (action) {
        case "download":
          onDownload(reportId)
          break
        case "archive":
          onArchive(reportId)
          break
        case "delete":
          onDelete(reportId)
          break
      }
    })
    setSelectedReports([])
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Generated Reports
            </CardTitle>
            <CardDescription>
              View and manage your generated reports
            </CardDescription>
          </div>
          <Badge variant="outline">
            {filteredReports.length} reports
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Filters */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search reports..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={(value) => setStatusFilter(value as ReportStatus | "all")}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="generating">Generating</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
                <SelectItem value="scheduled">Scheduled</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
              </SelectContent>
            </Select>
            <Select value={typeFilter} onValueChange={(value) => setTypeFilter(value as PhilippineReportType | "all")}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="SF2">SF2</SelectItem>
                <SelectItem value="SF4">SF4</SelectItem>
                <SelectItem value="custom_attendance">Custom</SelectItem>
                <SelectItem value="sms_notifications">SMS</SelectItem>
                <SelectItem value="deped_compliance">Compliance</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Bulk Actions */}
          {selectedReports.length > 0 && (
            <div className="flex items-center gap-2 p-3 bg-blue-50 dark:bg-blue-950 rounded-lg">
              <span className="text-sm text-blue-800 dark:text-blue-200">
                {selectedReports.length} report(s) selected
              </span>
              <div className="flex gap-2 ml-auto">
                <Button size="sm" variant="outline" onClick={() => handleBulkAction("download")}>
                  <Download className="h-4 w-4 mr-1" />
                  Download
                </Button>
                <Button size="sm" variant="outline" onClick={() => handleBulkAction("archive")}>
                  <Archive className="h-4 w-4 mr-1" />
                  Archive
                </Button>
                <Button size="sm" variant="outline" onClick={() => handleBulkAction("delete")}>
                  <Trash2 className="h-4 w-4 mr-1" />
                  Delete
                </Button>
              </div>
            </div>
          )}

          {/* Table */}
          <div className="border rounded-lg">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">
                    <Checkbox
                      checked={selectedReports.length === filteredReports.length && filteredReports.length > 0}
                      onCheckedChange={handleSelectAll}
                    />
                  </TableHead>
                  <TableHead>Report</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Generated</TableHead>
                  <TableHead>Size</TableHead>
                  <TableHead>Downloads</TableHead>
                  <TableHead className="w-12"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      <div className="flex items-center justify-center gap-2">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600" />
                        Loading reports...
                      </div>
                    </TableCell>
                  </TableRow>
                ) : filteredReports.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8 text-gray-500">
                      No reports found
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredReports.map((report) => (
                    <TableRow key={report.id}>
                      <TableCell>
                        <Checkbox
                          checked={selectedReports.includes(report.id)}
                          onCheckedChange={(checked) => handleSelectReport(report.id, checked as boolean)}
                        />
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="font-medium text-sm">{report.name}</div>
                          <div className="flex items-center gap-2 text-xs text-gray-500">
                            <User className="h-3 w-3" />
                            {report.generatedBy}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getTypeIcon(report.type)}
                          <span className="text-sm">{report.type}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          {getStatusBadge(report.status)}
                          {report.status === "generating" && report.progress && (
                            <div className="w-full bg-gray-200 rounded-full h-1">
                              <div 
                                className="bg-blue-600 h-1 rounded-full transition-all duration-300" 
                                style={{ width: `${report.progress}%` }}
                              />
                            </div>
                          )}
                          {report.status === "failed" && report.error && (
                            <div className="text-xs text-red-600 max-w-32 truncate" title={report.error}>
                              {report.error}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {formatDate(report.generatedAt)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {formatFileSize(report.fileSize || 0)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {report.downloadCount}
                        </div>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            {report.status === "completed" && (
                              <>
                                <DropdownMenuItem onClick={() => onPreview(report.id)}>
                                  <Eye className="h-4 w-4 mr-2" />
                                  Preview
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => onDownload(report.id)}>
                                  <Download className="h-4 w-4 mr-2" />
                                  Download
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => onShare(report.id)}>
                                  <Share className="h-4 w-4 mr-2" />
                                  Share
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                              </>
                            )}
                            <DropdownMenuItem onClick={() => onArchive(report.id)}>
                              <Archive className="h-4 w-4 mr-2" />
                              Archive
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              onClick={() => onDelete(report.id)}
                              className="text-red-600"
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
