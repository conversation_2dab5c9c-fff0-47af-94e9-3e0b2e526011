"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  Clock, 
  Mail, 
  Calendar, 
  Users, 
  Settings,
  Plus,
  Trash2,
  CheckCircle,
  AlertCircle,
  Send,
  Printer,
  Cloud,
  Save
} from "lucide-react"

const schedulingSchema = z.object({
  name: z.string().min(1, "Schedule name is required"),
  description: z.string().optional(),
  enabled: z.boolean(),
  frequency: z.enum(['daily', 'weekly', 'monthly', 'quarterly', 'yearly']),
  dayOfWeek: z.number().min(0).max(6).optional(),
  dayOfMonth: z.number().min(1).max(31).optional(),
  time: z.string().min(1, "Time is required"),
  timezone: z.string(),
  endDate: z.string().optional(),
  autoArchive: z.boolean(),
  emailDelivery: z.object({
    enabled: z.boolean(),
    recipients: z.array(z.string().email()),
    subject: z.string().min(1, "Subject is required"),
    message: z.string().optional(),
    attachFormats: z.array(z.enum(['pdf', 'excel', 'csv', 'word']))
  }),
  smsDelivery: z.object({
    enabled: z.boolean(),
    recipients: z.array(z.string()),
    message: z.string().max(160, "SMS message must be 160 characters or less")
  }),
  printDelivery: z.object({
    enabled: z.boolean(),
    printerName: z.string().optional(),
    copies: z.number().min(1).max(10),
    collate: z.boolean()
  }),
  cloudDelivery: z.object({
    enabled: z.boolean(),
    provider: z.enum(['google_drive', 'onedrive', 'dropbox']).optional(),
    folder: z.string().optional()
  })
})

type SchedulingFormData = z.infer<typeof schedulingSchema>

interface ReportSchedulingProps {
  onSave: (data: SchedulingFormData) => void
  onCancel: () => void
  defaultValues?: Partial<SchedulingFormData>
  isLoading?: boolean
}

const timezones = [
  { value: "Asia/Manila", label: "Asia/Manila (PHT)" },
  { value: "UTC", label: "UTC" },
  { value: "Asia/Tokyo", label: "Asia/Tokyo (JST)" },
  { value: "America/New_York", label: "America/New_York (EST)" }
]

const cloudProviders = [
  { value: "google_drive", label: "Google Drive", icon: "🔗" },
  { value: "onedrive", label: "Microsoft OneDrive", icon: "☁️" },
  { value: "dropbox", label: "Dropbox", icon: "📦" }
]

export function ReportScheduling({ 
  onSave, 
  onCancel, 
  defaultValues, 
  isLoading = false 
}: ReportSchedulingProps) {
  const [activeTab, setActiveTab] = useState("schedule")
  const [emailRecipients, setEmailRecipients] = useState<string[]>([])
  const [smsRecipients, setSmsRecipients] = useState<string[]>([])
  const [newEmailRecipient, setNewEmailRecipient] = useState("")
  const [newSmsRecipient, setNewSmsRecipient] = useState("")

  const form = useForm<SchedulingFormData>({
    resolver: zodResolver(schedulingSchema),
    defaultValues: {
      name: "",
      description: "",
      enabled: true,
      frequency: "weekly",
      time: "08:00",
      timezone: "Asia/Manila",
      autoArchive: true,
      emailDelivery: {
        enabled: false,
        recipients: [],
        subject: "Automated Report: {report_name}",
        message: "",
        attachFormats: ["pdf"]
      },
      smsDelivery: {
        enabled: false,
        recipients: [],
        message: "Your report {report_name} is ready for download."
      },
      printDelivery: {
        enabled: false,
        copies: 1,
        collate: true
      },
      cloudDelivery: {
        enabled: false,
        provider: "google_drive",
        folder: "Reports"
      },
      ...defaultValues
    }
  })

  const watchedValues = form.watch()

  const handleSubmit = (data: SchedulingFormData) => {
    const finalData = {
      ...data,
      emailDelivery: {
        ...data.emailDelivery,
        recipients: emailRecipients
      },
      smsDelivery: {
        ...data.smsDelivery,
        recipients: smsRecipients
      }
    }
    onSave(finalData)
  }

  const addEmailRecipient = () => {
    if (newEmailRecipient && !emailRecipients.includes(newEmailRecipient)) {
      setEmailRecipients([...emailRecipients, newEmailRecipient])
      setNewEmailRecipient("")
    }
  }

  const removeEmailRecipient = (email: string) => {
    setEmailRecipients(emailRecipients.filter(e => e !== email))
  }

  const addSmsRecipient = () => {
    if (newSmsRecipient && !smsRecipients.includes(newSmsRecipient)) {
      setSmsRecipients([...smsRecipients, newSmsRecipient])
      setNewSmsRecipient("")
    }
  }

  const removeSmsRecipient = (phone: string) => {
    setSmsRecipients(smsRecipients.filter(p => p !== phone))
  }

  const getFrequencyDescription = (frequency: string) => {
    switch (frequency) {
      case "daily":
        return "Report will be generated every day"
      case "weekly":
        return "Report will be generated every week"
      case "monthly":
        return "Report will be generated every month"
      case "quarterly":
        return "Report will be generated every quarter"
      case "yearly":
        return "Report will be generated every year"
      default:
        return ""
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Report Scheduling & Delivery
          </CardTitle>
          <CardDescription>
            Configure automated report generation and delivery options
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
              <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="schedule" className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    Schedule
                  </TabsTrigger>
                  <TabsTrigger value="email" className="flex items-center gap-2">
                    <Mail className="h-4 w-4" />
                    Email
                  </TabsTrigger>
                  <TabsTrigger value="delivery" className="flex items-center gap-2">
                    <Send className="h-4 w-4" />
                    Delivery
                  </TabsTrigger>
                  <TabsTrigger value="settings" className="flex items-center gap-2">
                    <Settings className="h-4 w-4" />
                    Settings
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="schedule" className="space-y-4">
                  <div className="grid gap-4 md:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Schedule Name *</FormLabel>
                          <FormControl>
                            <Input placeholder="e.g., Weekly SF2 Reports" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="enabled"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0 pt-6">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>Enable Schedule</FormLabel>
                            <FormDescription>
                              Activate automated report generation
                            </FormDescription>
                          </div>
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Textarea 
                            placeholder="Optional description of this schedule"
                            className="resize-none"
                            {...field} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid gap-4 md:grid-cols-3">
                    <FormField
                      control={form.control}
                      name="frequency"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Frequency</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select frequency" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="daily">Daily</SelectItem>
                              <SelectItem value="weekly">Weekly</SelectItem>
                              <SelectItem value="monthly">Monthly</SelectItem>
                              <SelectItem value="quarterly">Quarterly</SelectItem>
                              <SelectItem value="yearly">Yearly</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            {getFrequencyDescription(watchedValues.frequency)}
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="time"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Time</FormLabel>
                          <FormControl>
                            <Input type="time" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="timezone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Timezone</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select timezone" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {timezones.map((tz) => (
                                <SelectItem key={tz.value} value={tz.value}>
                                  {tz.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {watchedValues.frequency === "weekly" && (
                    <FormField
                      control={form.control}
                      name="dayOfWeek"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Day of Week</FormLabel>
                          <Select onValueChange={(value) => field.onChange(parseInt(value))} defaultValue={field.value?.toString()}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select day" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="0">Sunday</SelectItem>
                              <SelectItem value="1">Monday</SelectItem>
                              <SelectItem value="2">Tuesday</SelectItem>
                              <SelectItem value="3">Wednesday</SelectItem>
                              <SelectItem value="4">Thursday</SelectItem>
                              <SelectItem value="5">Friday</SelectItem>
                              <SelectItem value="6">Saturday</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}

                  {watchedValues.frequency === "monthly" && (
                    <FormField
                      control={form.control}
                      name="dayOfMonth"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Day of Month</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              min="1" 
                              max="31" 
                              placeholder="1-31"
                              {...field}
                              onChange={(e) => field.onChange(parseInt(e.target.value))}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}

                  <FormField
                    control={form.control}
                    name="endDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>End Date (Optional)</FormLabel>
                        <FormControl>
                          <Input type="date" {...field} />
                        </FormControl>
                        <FormDescription>
                          Leave empty for indefinite scheduling
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </TabsContent>

                <TabsContent value="email" className="space-y-4">
                  <FormField
                    control={form.control}
                    name="emailDelivery.enabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel className="flex items-center gap-2">
                            <Mail className="h-4 w-4" />
                            Enable Email Delivery
                          </FormLabel>
                          <FormDescription>
                            Automatically send reports via email when generated
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />

                  {watchedValues.emailDelivery?.enabled && (
                    <div className="space-y-4 pl-6 border-l-2 border-blue-200 dark:border-blue-800">
                      <div className="space-y-3">
                        <FormLabel>Email Recipients</FormLabel>
                        <div className="flex gap-2">
                          <Input
                            type="email"
                            placeholder="Enter email address"
                            value={newEmailRecipient}
                            onChange={(e) => setNewEmailRecipient(e.target.value)}
                            onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), addEmailRecipient())}
                          />
                          <Button type="button" onClick={addEmailRecipient} size="sm">
                            <Plus className="h-4 w-4" />
                          </Button>
                        </div>
                        <div className="flex flex-wrap gap-2">
                          {emailRecipients.map((email) => (
                            <Badge key={email} variant="secondary" className="flex items-center gap-1">
                              {email}
                              <button
                                type="button"
                                onClick={() => removeEmailRecipient(email)}
                                className="ml-1 hover:text-red-600"
                              >
                                <Trash2 className="h-3 w-3" />
                              </button>
                            </Badge>
                          ))}
                        </div>
                      </div>

                      <FormField
                        control={form.control}
                        name="emailDelivery.subject"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Email Subject</FormLabel>
                            <FormControl>
                              <Input placeholder="Report: {report_name}" {...field} />
                            </FormControl>
                            <FormDescription>
                              Use {"{report_name}"} for dynamic report name
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="emailDelivery.message"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Email Message</FormLabel>
                            <FormControl>
                              <Textarea 
                                placeholder="Optional custom message"
                                className="resize-none"
                                {...field} 
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="space-y-3">
                        <FormLabel>Attachment Formats</FormLabel>
                        <div className="grid gap-2 md:grid-cols-4">
                          {["pdf", "excel", "csv", "word"].map((format) => (
                            <FormField
                              key={format}
                              control={form.control}
                              name="emailDelivery.attachFormats"
                              render={({ field }) => (
                                <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                                  <FormControl>
                                    <Checkbox
                                      checked={field.value?.includes(format as any)}
                                      onCheckedChange={(checked) => {
                                        const current = field.value || []
                                        if (checked) {
                                          field.onChange([...current, format])
                                        } else {
                                          field.onChange(current.filter(f => f !== format))
                                        }
                                      }}
                                    />
                                  </FormControl>
                                  <FormLabel className="text-sm font-normal">
                                    {format.toUpperCase()}
                                  </FormLabel>
                                </FormItem>
                              )}
                            />
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="delivery" className="space-y-6">
                  {/* SMS Delivery */}
                  <div className="space-y-4">
                    <FormField
                      control={form.control}
                      name="smsDelivery.enabled"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>SMS Notifications</FormLabel>
                            <FormDescription>
                              Send SMS notifications when reports are ready
                            </FormDescription>
                          </div>
                        </FormItem>
                      )}
                    />

                    {watchedValues.smsDelivery?.enabled && (
                      <div className="space-y-3 pl-6 border-l-2 border-green-200 dark:border-green-800">
                        <div className="space-y-3">
                          <FormLabel>SMS Recipients</FormLabel>
                          <div className="flex gap-2">
                            <Input
                              placeholder="Enter phone number"
                              value={newSmsRecipient}
                              onChange={(e) => setNewSmsRecipient(e.target.value)}
                              onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), addSmsRecipient())}
                            />
                            <Button type="button" onClick={addSmsRecipient} size="sm">
                              <Plus className="h-4 w-4" />
                            </Button>
                          </div>
                          <div className="flex flex-wrap gap-2">
                            {smsRecipients.map((phone) => (
                              <Badge key={phone} variant="secondary" className="flex items-center gap-1">
                                {phone}
                                <button
                                  type="button"
                                  onClick={() => removeSmsRecipient(phone)}
                                  className="ml-1 hover:text-red-600"
                                >
                                  <Trash2 className="h-3 w-3" />
                                </button>
                              </Badge>
                            ))}
                          </div>
                        </div>

                        <FormField
                          control={form.control}
                          name="smsDelivery.message"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>SMS Message</FormLabel>
                              <FormControl>
                                <Textarea 
                                  placeholder="Your report is ready for download"
                                  className="resize-none"
                                  maxLength={160}
                                  {...field} 
                                />
                              </FormControl>
                              <FormDescription>
                                {field.value?.length || 0}/160 characters
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    )}
                  </div>

                  <Separator />

                  {/* Print Delivery */}
                  <div className="space-y-4">
                    <FormField
                      control={form.control}
                      name="printDelivery.enabled"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel className="flex items-center gap-2">
                              <Printer className="h-4 w-4" />
                              Auto Print
                            </FormLabel>
                            <FormDescription>
                              Automatically print reports when generated
                            </FormDescription>
                          </div>
                        </FormItem>
                      )}
                    />

                    {watchedValues.printDelivery?.enabled && (
                      <div className="grid gap-4 md:grid-cols-2 pl-6 border-l-2 border-purple-200 dark:border-purple-800">
                        <FormField
                          control={form.control}
                          name="printDelivery.printerName"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Printer Name</FormLabel>
                              <FormControl>
                                <Input placeholder="Default printer" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="printDelivery.copies"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Number of Copies</FormLabel>
                              <FormControl>
                                <Input 
                                  type="number" 
                                  min="1" 
                                  max="10" 
                                  {...field}
                                  onChange={(e) => field.onChange(parseInt(e.target.value))}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    )}
                  </div>

                  <Separator />

                  {/* Cloud Delivery */}
                  <div className="space-y-4">
                    <FormField
                      control={form.control}
                      name="cloudDelivery.enabled"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel className="flex items-center gap-2">
                              <Cloud className="h-4 w-4" />
                              Cloud Storage
                            </FormLabel>
                            <FormDescription>
                              Upload reports to cloud storage
                            </FormDescription>
                          </div>
                        </FormItem>
                      )}
                    />

                    {watchedValues.cloudDelivery?.enabled && (
                      <div className="grid gap-4 md:grid-cols-2 pl-6 border-l-2 border-orange-200 dark:border-orange-800">
                        <FormField
                          control={form.control}
                          name="cloudDelivery.provider"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Cloud Provider</FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value}>
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select provider" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {cloudProviders.map((provider) => (
                                    <SelectItem key={provider.value} value={provider.value}>
                                      {provider.icon} {provider.label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="cloudDelivery.folder"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Folder Path</FormLabel>
                              <FormControl>
                                <Input placeholder="Reports" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    )}
                  </div>
                </TabsContent>

                <TabsContent value="settings" className="space-y-4">
                  <FormField
                    control={form.control}
                    name="autoArchive"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>Auto Archive</FormLabel>
                          <FormDescription>
                            Automatically archive old reports after 30 days
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />

                  <div className="p-4 bg-blue-50 dark:bg-blue-950 rounded-lg">
                    <div className="flex items-start gap-3">
                      <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5" />
                      <div className="space-y-1">
                        <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200">
                          Schedule Summary
                        </h4>
                        <div className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                          <p>• Frequency: {watchedValues.frequency}</p>
                          <p>• Time: {watchedValues.time} ({watchedValues.timezone})</p>
                          {watchedValues.emailDelivery?.enabled && (
                            <p>• Email delivery to {emailRecipients.length} recipient(s)</p>
                          )}
                          {watchedValues.smsDelivery?.enabled && (
                            <p>• SMS notifications to {smsRecipients.length} recipient(s)</p>
                          )}
                          {watchedValues.printDelivery?.enabled && (
                            <p>• Auto print {watchedValues.printDelivery.copies} copies</p>
                          )}
                          {watchedValues.cloudDelivery?.enabled && (
                            <p>• Upload to {watchedValues.cloudDelivery.provider}</p>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>

              <div className="flex justify-between pt-6 border-t">
                <Button type="button" variant="outline" onClick={onCancel}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Save Schedule
                    </>
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  )
}
