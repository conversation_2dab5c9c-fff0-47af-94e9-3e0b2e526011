"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { DatePicker } from "@/components/ui/date-picker"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { 
  Calendar, 
  Filter, 
  FileText, 
  Mail, 
  Printer, 
  Clock,
  Users,
  GraduationCap,
  UserCheck,
  Settings,
  Download
} from "lucide-react"
import { PhilippineReportType, ReportFormat, ReportFormData } from "@/types"

const reportParametersSchema = z.object({
  reportType: z.string(),
  templateId: z.string().optional(),
  name: z.string().min(1, "Report name is required"),
  description: z.string().optional(),
  parameters: z.object({
    dateRange: z.object({
      start: z.string().min(1, "Start date is required"),
      end: z.string().min(1, "End date is required")
    }),
    filters: z.object({
      grades: z.array(z.string()),
      sections: z.array(z.string()),
      courses: z.array(z.string()),
      teachers: z.array(z.string())
    }),
    format: z.enum(['pdf', 'excel', 'csv', 'print', 'word']),
    includeCharts: z.boolean(),
    includeStatistics: z.boolean(),
    includeSignatures: z.boolean()
  }),
  scheduling: z.object({
    enabled: z.boolean(),
    frequency: z.enum(['daily', 'weekly', 'monthly']).optional(),
    time: z.string().optional(),
    recipients: z.array(z.string()).optional()
  }).optional(),
  delivery: z.object({
    email: z.object({
      enabled: z.boolean(),
      recipients: z.array(z.string()),
      subject: z.string().optional(),
      message: z.string().optional()
    }),
    print: z.object({
      enabled: z.boolean(),
      copies: z.number().min(1).max(10)
    })
  }).optional()
})

type ReportParametersFormData = z.infer<typeof reportParametersSchema>

interface ReportParametersFormProps {
  reportType: PhilippineReportType
  onSubmit: (data: ReportFormData) => void
  onPreview: (data: ReportFormData) => void
  isLoading?: boolean
  defaultValues?: Partial<ReportFormData>
}

// Mock data - in real app, these would come from API
const mockGrades = ['Grade 1', 'Grade 2', 'Grade 3', 'Grade 4', 'Grade 5', 'Grade 6']
const mockSections = ['Section A', 'Section B', 'Section C', 'Section D']
const mockCourses = ['BSIT', 'BSBA', 'BSED', 'BSHM', 'BSCS']
const mockTeachers = ['Ms. Garcia', 'Mr. Santos', 'Mrs. Cruz', 'Mr. Reyes', 'Ms. Dela Cruz']

export function ReportParametersForm({ 
  reportType, 
  onSubmit, 
  onPreview, 
  isLoading = false,
  defaultValues 
}: ReportParametersFormProps) {
  const [activeTab, setActiveTab] = useState("basic")

  const form = useForm<ReportParametersFormData>({
    resolver: zodResolver(reportParametersSchema),
    defaultValues: {
      reportType,
      name: "",
      description: "",
      parameters: {
        dateRange: {
          start: "",
          end: ""
        },
        filters: {
          grades: [],
          sections: [],
          courses: [],
          teachers: []
        },
        format: 'pdf',
        includeCharts: true,
        includeStatistics: true,
        includeSignatures: reportType === 'SF2' || reportType === 'SF4'
      },
      scheduling: {
        enabled: false
      },
      delivery: {
        email: {
          enabled: false,
          recipients: []
        },
        print: {
          enabled: false,
          copies: 1
        }
      },
      ...defaultValues
    }
  })

  const watchedValues = form.watch()

  const handleSubmit = (data: ReportParametersFormData) => {
    onSubmit(data as ReportFormData)
  }

  const handlePreview = () => {
    const data = form.getValues()
    onPreview(data as ReportFormData)
  }

  const getReportTypeDescription = (type: PhilippineReportType) => {
    switch (type) {
      case 'SF2':
        return 'Daily Attendance Report of Learners - Official DepEd Form'
      case 'SF4':
        return 'School Register - Official DepEd Form'
      case 'custom_attendance':
        return 'Custom Attendance Report with flexible parameters'
      case 'sms_notifications':
        return 'SMS Notification delivery and status report'
      case 'deped_compliance':
        return 'DepEd Compliance and audit report'
      default:
        return 'Report generation parameters'
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Report Configuration
          </CardTitle>
          <CardDescription>
            {getReportTypeDescription(reportType)}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
              <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="basic" className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    Basic
                  </TabsTrigger>
                  <TabsTrigger value="filters" className="flex items-center gap-2">
                    <Filter className="h-4 w-4" />
                    Filters
                  </TabsTrigger>
                  <TabsTrigger value="scheduling" className="flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    Schedule
                  </TabsTrigger>
                  <TabsTrigger value="delivery" className="flex items-center gap-2">
                    <Mail className="h-4 w-4" />
                    Delivery
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="basic" className="space-y-4">
                  <div className="grid gap-4 md:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Report Name *</FormLabel>
                          <FormControl>
                            <Input placeholder="Enter report name" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="parameters.format"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Output Format</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select format" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="pdf">PDF Document</SelectItem>
                              <SelectItem value="excel">Excel Spreadsheet</SelectItem>
                              <SelectItem value="csv">CSV File</SelectItem>
                              <SelectItem value="word">Word Document</SelectItem>
                              <SelectItem value="print">Print Ready</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Input placeholder="Optional description" {...field} />
                        </FormControl>
                        <FormDescription>
                          Brief description of this report
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid gap-4 md:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="parameters.dateRange.start"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Start Date *</FormLabel>
                          <FormControl>
                            <Input type="date" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="parameters.dateRange.end"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>End Date *</FormLabel>
                          <FormControl>
                            <Input type="date" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="space-y-3">
                    <h4 className="text-sm font-medium">Report Options</h4>
                    <div className="grid gap-3 md:grid-cols-3">
                      <FormField
                        control={form.control}
                        name="parameters.includeCharts"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel>Include Charts</FormLabel>
                              <FormDescription>
                                Add visual charts and graphs
                              </FormDescription>
                            </div>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="parameters.includeStatistics"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel>Include Statistics</FormLabel>
                              <FormDescription>
                                Add statistical summaries
                              </FormDescription>
                            </div>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="parameters.includeSignatures"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel>Digital Signatures</FormLabel>
                              <FormDescription>
                                Include signature fields
                              </FormDescription>
                            </div>
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="filters" className="space-y-4">
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-3">
                      <FormLabel className="flex items-center gap-2">
                        <GraduationCap className="h-4 w-4" />
                        Grades/Year Levels
                      </FormLabel>
                      <div className="grid gap-2">
                        {mockGrades.map((grade) => (
                          <FormField
                            key={grade}
                            control={form.control}
                            name="parameters.filters.grades"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                                <FormControl>
                                  <Checkbox
                                    checked={field.value?.includes(grade)}
                                    onCheckedChange={(checked) => {
                                      const current = field.value || []
                                      if (checked) {
                                        field.onChange([...current, grade])
                                      } else {
                                        field.onChange(current.filter(g => g !== grade))
                                      }
                                    }}
                                  />
                                </FormControl>
                                <FormLabel className="text-sm font-normal">
                                  {grade}
                                </FormLabel>
                              </FormItem>
                            )}
                          />
                        ))}
                      </div>
                    </div>

                    <div className="space-y-3">
                      <FormLabel className="flex items-center gap-2">
                        <Users className="h-4 w-4" />
                        Sections
                      </FormLabel>
                      <div className="grid gap-2">
                        {mockSections.map((section) => (
                          <FormField
                            key={section}
                            control={form.control}
                            name="parameters.filters.sections"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                                <FormControl>
                                  <Checkbox
                                    checked={field.value?.includes(section)}
                                    onCheckedChange={(checked) => {
                                      const current = field.value || []
                                      if (checked) {
                                        field.onChange([...current, section])
                                      } else {
                                        field.onChange(current.filter(s => s !== section))
                                      }
                                    }}
                                  />
                                </FormControl>
                                <FormLabel className="text-sm font-normal">
                                  {section}
                                </FormLabel>
                              </FormItem>
                            )}
                          />
                        ))}
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-3">
                      <FormLabel>Courses/Programs</FormLabel>
                      <div className="grid gap-2">
                        {mockCourses.map((course) => (
                          <FormField
                            key={course}
                            control={form.control}
                            name="parameters.filters.courses"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                                <FormControl>
                                  <Checkbox
                                    checked={field.value?.includes(course)}
                                    onCheckedChange={(checked) => {
                                      const current = field.value || []
                                      if (checked) {
                                        field.onChange([...current, course])
                                      } else {
                                        field.onChange(current.filter(c => c !== course))
                                      }
                                    }}
                                  />
                                </FormControl>
                                <FormLabel className="text-sm font-normal">
                                  {course}
                                </FormLabel>
                              </FormItem>
                            )}
                          />
                        ))}
                      </div>
                    </div>

                    <div className="space-y-3">
                      <FormLabel className="flex items-center gap-2">
                        <UserCheck className="h-4 w-4" />
                        Teachers/Advisers
                      </FormLabel>
                      <div className="grid gap-2">
                        {mockTeachers.map((teacher) => (
                          <FormField
                            key={teacher}
                            control={form.control}
                            name="parameters.filters.teachers"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                                <FormControl>
                                  <Checkbox
                                    checked={field.value?.includes(teacher)}
                                    onCheckedChange={(checked) => {
                                      const current = field.value || []
                                      if (checked) {
                                        field.onChange([...current, teacher])
                                      } else {
                                        field.onChange(current.filter(t => t !== teacher))
                                      }
                                    }}
                                  />
                                </FormControl>
                                <FormLabel className="text-sm font-normal">
                                  {teacher}
                                </FormLabel>
                              </FormItem>
                            )}
                          />
                        ))}
                      </div>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="scheduling" className="space-y-4">
                  <FormField
                    control={form.control}
                    name="scheduling.enabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>Enable Automated Scheduling</FormLabel>
                          <FormDescription>
                            Automatically generate this report on a schedule
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />

                  {watchedValues.scheduling?.enabled && (
                    <div className="grid gap-4 md:grid-cols-2 pl-6 border-l-2 border-blue-200 dark:border-blue-800">
                      <FormField
                        control={form.control}
                        name="scheduling.frequency"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Frequency</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select frequency" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="daily">Daily</SelectItem>
                                <SelectItem value="weekly">Weekly</SelectItem>
                                <SelectItem value="monthly">Monthly</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="scheduling.time"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Time</FormLabel>
                            <FormControl>
                              <Input type="time" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="delivery" className="space-y-4">
                  <div className="space-y-6">
                    <div className="space-y-4">
                      <FormField
                        control={form.control}
                        name="delivery.email.enabled"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel className="flex items-center gap-2">
                                <Mail className="h-4 w-4" />
                                Email Delivery
                              </FormLabel>
                              <FormDescription>
                                Send report via email when generated
                              </FormDescription>
                            </div>
                          </FormItem>
                        )}
                      />

                      {watchedValues.delivery?.email?.enabled && (
                        <div className="pl-6 border-l-2 border-green-200 dark:border-green-800 space-y-3">
                          <FormField
                            control={form.control}
                            name="delivery.email.subject"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Email Subject</FormLabel>
                                <FormControl>
                                  <Input placeholder="Report: {report_name}" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      )}
                    </div>

                    <div className="space-y-4">
                      <FormField
                        control={form.control}
                        name="delivery.print.enabled"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel className="flex items-center gap-2">
                                <Printer className="h-4 w-4" />
                                Auto Print
                              </FormLabel>
                              <FormDescription>
                                Automatically print when report is generated
                              </FormDescription>
                            </div>
                          </FormItem>
                        )}
                      />

                      {watchedValues.delivery?.print?.enabled && (
                        <div className="pl-6 border-l-2 border-purple-200 dark:border-purple-800">
                          <FormField
                            control={form.control}
                            name="delivery.print.copies"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Number of Copies</FormLabel>
                                <FormControl>
                                  <Input 
                                    type="number" 
                                    min="1" 
                                    max="10" 
                                    {...field}
                                    onChange={(e) => field.onChange(parseInt(e.target.value))}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      )}
                    </div>
                  </div>
                </TabsContent>
              </Tabs>

              <div className="flex justify-between pt-6 border-t">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handlePreview}
                  disabled={isLoading}
                >
                  Preview Report
                </Button>
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Download className="h-4 w-4 mr-2" />
                      Generate Report
                    </>
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  )
}
