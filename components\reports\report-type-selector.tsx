"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { 
  FileText, 
  Users, 
  MessageSquare, 
  BarChart3, 
  CheckCircle, 
  Clock,
  Shield,
  FileCheck
} from "lucide-react"
import { PhilippineReportType } from "@/types"

interface ReportTypeOption {
  type: PhilippineReportType
  name: string
  description: string
  depedCode?: string
  icon: React.ComponentType<{ className?: string }>
  isOfficial: boolean
  estimatedTime: string
  features: string[]
  complianceLevel: 'full' | 'partial' | 'custom'
}

const reportTypes: ReportTypeOption[] = [
  {
    type: 'SF2',
    name: 'SF2 - Daily Attendance Report of Learners',
    description: 'Official DepEd form for recording daily attendance of learners by grade and section',
    depedCode: 'SF2',
    icon: FileText,
    isOfficial: true,
    estimatedTime: '2-3 minutes',
    features: [
      'Daily attendance tracking',
      'Male/Female breakdown',
      'Monthly summaries',
      'DepEd compliant format',
      'Digital signatures'
    ],
    complianceLevel: 'full'
  },
  {
    type: 'SF4',
    name: 'SF4 - School Register',
    description: 'Official DepEd school register containing learner information and enrollment data',
    depedCode: 'SF4',
    icon: Users,
    isOfficial: true,
    estimatedTime: '3-5 minutes',
    features: [
      'Complete learner profiles',
      'LRN tracking',
      'Guardian information',
      'Class statistics',
      'Official DepEd format'
    ],
    complianceLevel: 'full'
  },
  {
    type: 'custom_attendance',
    name: 'Custom Attendance Reports',
    description: 'Flexible attendance reports with customizable parameters and formats',
    icon: BarChart3,
    isOfficial: false,
    estimatedTime: '1-2 minutes',
    features: [
      'Flexible date ranges',
      'Multiple export formats',
      'Custom filters',
      'Charts and analytics',
      'Batch generation'
    ],
    complianceLevel: 'custom'
  },
  {
    type: 'sms_notifications',
    name: 'SMS Notification Reports',
    description: 'Reports on SMS notifications sent to parents and guardians',
    icon: MessageSquare,
    isOfficial: false,
    estimatedTime: '1 minute',
    features: [
      'Delivery status tracking',
      'Message history',
      'Recipient analytics',
      'Cost analysis',
      'Failed delivery reports'
    ],
    complianceLevel: 'custom'
  },
  {
    type: 'deped_compliance',
    name: 'DepEd Compliance Reports',
    description: 'Comprehensive compliance reports for DepEd requirements and audits',
    icon: Shield,
    isOfficial: true,
    estimatedTime: '5-10 minutes',
    features: [
      'Compliance checking',
      'Audit trails',
      'Regulatory requirements',
      'Documentation status',
      'Risk assessment'
    ],
    complianceLevel: 'full'
  }
]

interface ReportTypeSelectorProps {
  selectedType?: PhilippineReportType
  onTypeSelect: (type: PhilippineReportType) => void
  disabled?: boolean
}

export function ReportTypeSelector({ selectedType, onTypeSelect, disabled = false }: ReportTypeSelectorProps) {
  const [hoveredType, setHoveredType] = useState<PhilippineReportType | null>(null)

  const getComplianceBadgeColor = (level: 'full' | 'partial' | 'custom') => {
    switch (level) {
      case 'full':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'partial':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      case 'custom':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
    }
  }

  const getComplianceText = (level: 'full' | 'partial' | 'custom') => {
    switch (level) {
      case 'full':
        return 'Full DepEd Compliance'
      case 'partial':
        return 'Partial Compliance'
      case 'custom':
        return 'Custom Format'
    }
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Select Report Type
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Choose the type of report you want to generate
          </p>
        </div>
        {selectedType && (
          <Badge variant="outline" className="flex items-center gap-1">
            <CheckCircle className="h-3 w-3" />
            Selected
          </Badge>
        )}
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {reportTypes.map((reportType) => {
          const Icon = reportType.icon
          const isSelected = selectedType === reportType.type
          const isHovered = hoveredType === reportType.type

          return (
            <Card
              key={reportType.type}
              className={`cursor-pointer transition-all duration-200 ${
                isSelected
                  ? 'ring-2 ring-blue-500 border-blue-500 bg-blue-50 dark:bg-blue-950'
                  : isHovered
                  ? 'border-gray-300 dark:border-gray-600 shadow-md'
                  : 'border-gray-200 dark:border-gray-700'
              } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
              onMouseEnter={() => !disabled && setHoveredType(reportType.type)}
              onMouseLeave={() => setHoveredType(null)}
              onClick={() => !disabled && onTypeSelect(reportType.type)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    <div className={`p-2 rounded-lg ${
                      isSelected 
                        ? 'bg-blue-100 dark:bg-blue-900' 
                        : 'bg-gray-100 dark:bg-gray-800'
                    }`}>
                      <Icon className={`h-5 w-5 ${
                        isSelected 
                          ? 'text-blue-600 dark:text-blue-400' 
                          : 'text-gray-600 dark:text-gray-400'
                      }`} />
                    </div>
                    <div className="flex flex-col gap-1">
                      {reportType.isOfficial && (
                        <Badge variant="outline" className="w-fit text-xs">
                          <FileCheck className="h-3 w-3 mr-1" />
                          Official
                        </Badge>
                      )}
                      {reportType.depedCode && (
                        <Badge variant="secondary" className="w-fit text-xs">
                          {reportType.depedCode}
                        </Badge>
                      )}
                    </div>
                  </div>
                  <Badge className={`text-xs ${getComplianceBadgeColor(reportType.complianceLevel)}`}>
                    {getComplianceText(reportType.complianceLevel)}
                  </Badge>
                </div>
                <CardTitle className="text-base leading-tight">
                  {reportType.name}
                </CardTitle>
                <CardDescription className="text-sm">
                  {reportType.description}
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-3">
                  <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                    <Clock className="h-4 w-4" />
                    <span>Est. {reportType.estimatedTime}</span>
                  </div>
                  
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                      Key Features:
                    </h4>
                    <ul className="space-y-1">
                      {reportType.features.slice(0, 3).map((feature, index) => (
                        <li key={index} className="flex items-center gap-2 text-xs text-gray-600 dark:text-gray-400">
                          <div className="h-1 w-1 bg-gray-400 rounded-full" />
                          {feature}
                        </li>
                      ))}
                      {reportType.features.length > 3 && (
                        <li className="text-xs text-gray-500 dark:text-gray-500">
                          +{reportType.features.length - 3} more features
                        </li>
                      )}
                    </ul>
                  </div>

                  <Button
                    variant={isSelected ? "default" : "outline"}
                    size="sm"
                    className="w-full mt-3"
                    disabled={disabled}
                  >
                    {isSelected ? (
                      <>
                        <CheckCircle className="h-4 w-4 mr-2" />
                        Selected
                      </>
                    ) : (
                      'Select This Type'
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {selectedType && (
        <Card className="bg-blue-50 dark:bg-blue-950 border-blue-200 dark:border-blue-800">
          <CardContent className="pt-4">
            <div className="flex items-center gap-2 text-blue-800 dark:text-blue-200">
              <CheckCircle className="h-5 w-5" />
              <span className="font-medium">
                {reportTypes.find(rt => rt.type === selectedType)?.name} selected
              </span>
            </div>
            <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
              You can now configure the report parameters and generate your report.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
