"use client"

import { useState } from "react"
import { MainLayout } from "@/components/layout/main-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog"
import { useToast } from "@/hooks/use-toast"
import {
  Settings,
  School,
  MessageSquare,
  QrCode,
  Users,
  Database,
  Palette,
  Save,
  Download,
  Upload,
  Eye,
  Edit,
  Trash2,
  Plus,
  Clock,
  Shield,
  Bell,
  Calendar,
  FileText,
  Printer,
  Key,
  Monitor,
  Smartphone,
  Mail,
  Globe,
  Phone
} from "lucide-react"
import type {
  GeneralSettings,
  ClassSchedule,
  AttendanceRules,
  SMSSettings,
  MessageTemplate,
  QRCodeSettings,
  UserRole,
  PasswordPolicy,
  SessionSettings,
  BackupSettings,
  AppearanceSettings
} from "@/types"

export default function SettingsPage() {
  const { toast } = useToast()

  // State for different settings sections
  const [generalSettings, setGeneralSettings] = useState<GeneralSettings>({
    schoolName: "Tanauan School of Arts and Trade",
    schoolAddress: "Brgy. Cabuyan, Tanauan, Leyte",
    schoolPhone: "+63 53 325 1234",
    schoolEmail: "<EMAIL>",
    schoolWebsite: "https://tsat.edu.ph",
    academicYear: "2024-2025",
    semester: "first",
    startDate: new Date("2024-08-01"),
    endDate: new Date("2025-05-31"),
    timeZone: "Asia/Manila",
    dateFormat: "MM/DD/YYYY",
    language: "en"
  })

  const [attendanceRules, setAttendanceRules] = useState<AttendanceRules>({
    gracePeriodMinutes: 15,
    autoLogoutMinutes: 30,
    requireCheckout: false,
    allowManualEntry: true,
    weekendTracking: false,
    lateThresholdMinutes: 15,
    absentThresholdMinutes: 60,
    excusedAbsenceRequiresApproval: true
  })

  const [smsSettings, setSmsSettings] = useState<SMSSettings>({
    apiKey: "",
    apiSecret: "",
    senderName: "TSAT",
    enabled: true,
    dailyLimit: 100,
    monthlyLimit: 3000,
    parentNotifications: true,
    teacherNotifications: true,
    adminNotifications: true
  })

  const [qrSettings, setQrSettings] = useState<QRCodeSettings>({
    expirationMinutes: 1440, // 24 hours
    securityLevel: "medium",
    batchSize: 50,
    printLayout: "grid",
    includeStudentPhoto: true,
    includeSchoolLogo: true,
    regenerateOnScan: false,
    maxScansPerCode: 10
  })

  const [backupSettings, setBackupSettings] = useState<BackupSettings>({
    autoBackupEnabled: true,
    backupFrequency: "daily",
    backupTime: "03:00",
    retentionDays: 30,
    includeAttachments: true,
    cloudBackupEnabled: false
  })

  const [appearanceSettings, setAppearanceSettings] = useState<AppearanceSettings>({
    theme: "system",
    primaryColor: "#3b82f6",
    accentColor: "#10b981",
    fontSize: "medium",
    compactMode: false,
    showAnimations: true
  })

  const [activeTab, setActiveTab] = useState("general")

  const handleSaveSettings = (section: string) => {
    toast({
      title: "Settings Saved",
      description: `${section} settings have been saved successfully.`,
    })
  }

  const handleExportSettings = () => {
    const allSettings = {
      general: generalSettings,
      attendance: attendanceRules,
      sms: smsSettings,
      qr: qrSettings,
      backup: backupSettings,
      appearance: appearanceSettings
    }

    const dataStr = JSON.stringify(allSettings, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = 'qrsams-settings.json'
    link.click()

    toast({
      title: "Settings Exported",
      description: "Settings have been exported successfully.",
    })
  }

  return (
    <MainLayout title="Settings">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              System Settings
            </h2>
            <p className="text-gray-600 dark:text-gray-400">
              Configure system preferences and school information
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleExportSettings}>
              <Download className="h-4 w-4 mr-2" />
              Export Settings
            </Button>
            <Button variant="outline">
              <Upload className="h-4 w-4 mr-2" />
              Import Settings
            </Button>
          </div>
        </div>

        {/* Tabbed Settings Interface */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="general" className="flex items-center gap-2">
              <School className="h-4 w-4" />
              General
            </TabsTrigger>
            <TabsTrigger value="sms" className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              SMS
            </TabsTrigger>
            <TabsTrigger value="qr" className="flex items-center gap-2">
              <QrCode className="h-4 w-4" />
              QR Code
            </TabsTrigger>
            <TabsTrigger value="users" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Users
            </TabsTrigger>
            <TabsTrigger value="backup" className="flex items-center gap-2">
              <Database className="h-4 w-4" />
              Backup
            </TabsTrigger>
            <TabsTrigger value="appearance" className="flex items-center gap-2">
              <Palette className="h-4 w-4" />
              Appearance
            </TabsTrigger>
          </TabsList>

          {/* General Settings Tab */}
          <TabsContent value="general" className="space-y-6">
            {/* School Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <School className="h-5 w-5" />
                  School Information
                </CardTitle>
                <CardDescription>
                  Basic information about your educational institution
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <Label htmlFor="schoolName">School Name</Label>
                    <Input
                      id="schoolName"
                      value={generalSettings.schoolName}
                      onChange={(e) => setGeneralSettings(prev => ({ ...prev, schoolName: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="schoolEmail">School Email</Label>
                    <Input
                      id="schoolEmail"
                      type="email"
                      value={generalSettings.schoolEmail}
                      onChange={(e) => setGeneralSettings(prev => ({ ...prev, schoolEmail: e.target.value }))}
                    />
                  </div>
                  <div className="md:col-span-2">
                    <Label htmlFor="schoolAddress">Address</Label>
                    <Input
                      id="schoolAddress"
                      value={generalSettings.schoolAddress}
                      onChange={(e) => setGeneralSettings(prev => ({ ...prev, schoolAddress: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="schoolPhone">Phone Number</Label>
                    <Input
                      id="schoolPhone"
                      value={generalSettings.schoolPhone}
                      onChange={(e) => setGeneralSettings(prev => ({ ...prev, schoolPhone: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="schoolWebsite">Website</Label>
                    <Input
                      id="schoolWebsite"
                      value={generalSettings.schoolWebsite}
                      onChange={(e) => setGeneralSettings(prev => ({ ...prev, schoolWebsite: e.target.value }))}
                    />
                  </div>
                </div>
                <Button onClick={() => handleSaveSettings("School Information")}>
                  <Save className="h-4 w-4 mr-2" />
                  Save School Information
                </Button>
              </CardContent>
            </Card>

            {/* Academic Year Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Academic Year Settings
                </CardTitle>
                <CardDescription>
                  Configure academic year and semester information
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-3">
                  <div>
                    <Label htmlFor="academicYear">Academic Year</Label>
                    <Select value={generalSettings.academicYear} onValueChange={(value) => setGeneralSettings(prev => ({ ...prev, academicYear: value }))}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="2024-2025">2024-2025</SelectItem>
                        <SelectItem value="2023-2024">2023-2024</SelectItem>
                        <SelectItem value="2025-2026">2025-2026</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="semester">Current Semester</Label>
                    <Select value={generalSettings.semester} onValueChange={(value: "first" | "second" | "summer") => setGeneralSettings(prev => ({ ...prev, semester: value }))}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="first">First Semester</SelectItem>
                        <SelectItem value="second">Second Semester</SelectItem>
                        <SelectItem value="summer">Summer</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="timeZone">Time Zone</Label>
                    <Select value={generalSettings.timeZone} onValueChange={(value) => setGeneralSettings(prev => ({ ...prev, timeZone: value }))}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Asia/Manila">Asia/Manila</SelectItem>
                        <SelectItem value="UTC">UTC</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <Button onClick={() => handleSaveSettings("Academic Year")}>
                  <Save className="h-4 w-4 mr-2" />
                  Save Academic Settings
                </Button>
              </CardContent>
            </Card>

            {/* Attendance Rules */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Attendance Rules & Policies
                </CardTitle>
                <CardDescription>
                  Configure attendance tracking parameters and policies
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <Label htmlFor="gracePeriod">Grace Period (minutes)</Label>
                    <Input
                      id="gracePeriod"
                      type="number"
                      value={attendanceRules.gracePeriodMinutes}
                      onChange={(e) => setAttendanceRules(prev => ({ ...prev, gracePeriodMinutes: parseInt(e.target.value) }))}
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Time allowed for late arrivals before marking as late
                    </p>
                  </div>
                  <div>
                    <Label htmlFor="autoLogout">Auto Logout (minutes)</Label>
                    <Input
                      id="autoLogout"
                      type="number"
                      value={attendanceRules.autoLogoutMinutes}
                      onChange={(e) => setAttendanceRules(prev => ({ ...prev, autoLogoutMinutes: parseInt(e.target.value) }))}
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Automatic logout time for inactive sessions
                    </p>
                  </div>
                  <div>
                    <Label htmlFor="lateThreshold">Late Threshold (minutes)</Label>
                    <Input
                      id="lateThreshold"
                      type="number"
                      value={attendanceRules.lateThresholdMinutes}
                      onChange={(e) => setAttendanceRules(prev => ({ ...prev, lateThresholdMinutes: parseInt(e.target.value) }))}
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Minutes after start time to mark as late
                    </p>
                  </div>
                  <div>
                    <Label htmlFor="absentThreshold">Absent Threshold (minutes)</Label>
                    <Input
                      id="absentThreshold"
                      type="number"
                      value={attendanceRules.absentThresholdMinutes}
                      onChange={(e) => setAttendanceRules(prev => ({ ...prev, absentThresholdMinutes: parseInt(e.target.value) }))}
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Minutes after start time to mark as absent
                    </p>
                  </div>
                </div>

                <Separator />

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Require Check-out</div>
                      <div className="text-sm text-gray-500">Students must scan QR code when leaving</div>
                    </div>
                    <Switch
                      checked={attendanceRules.requireCheckout}
                      onCheckedChange={(checked) => setAttendanceRules(prev => ({ ...prev, requireCheckout: checked }))}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Allow Manual Entry</div>
                      <div className="text-sm text-gray-500">Permit manual attendance entry by staff</div>
                    </div>
                    <Switch
                      checked={attendanceRules.allowManualEntry}
                      onCheckedChange={(checked) => setAttendanceRules(prev => ({ ...prev, allowManualEntry: checked }))}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Weekend Tracking</div>
                      <div className="text-sm text-gray-500">Track attendance on weekends</div>
                    </div>
                    <Switch
                      checked={attendanceRules.weekendTracking}
                      onCheckedChange={(checked) => setAttendanceRules(prev => ({ ...prev, weekendTracking: checked }))}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Excused Absence Approval</div>
                      <div className="text-sm text-gray-500">Require approval for excused absences</div>
                    </div>
                    <Switch
                      checked={attendanceRules.excusedAbsenceRequiresApproval}
                      onCheckedChange={(checked) => setAttendanceRules(prev => ({ ...prev, excusedAbsenceRequiresApproval: checked }))}
                    />
                  </div>
                </div>
                <Button onClick={() => handleSaveSettings("Attendance Rules")}>
                  <Save className="h-4 w-4 mr-2" />
                  Save Attendance Settings
                </Button>
              </CardContent>
            </Card>

            {/* Class Schedules */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Class Schedules Configuration
                </CardTitle>
                <CardDescription>
                  Manage class schedules and time slots
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <p className="text-sm text-gray-600">Configure class schedules for attendance tracking</p>
                  <Button size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Schedule
                  </Button>
                </div>

                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Class Name</TableHead>
                      <TableHead>Time</TableHead>
                      <TableHead>Days</TableHead>
                      <TableHead>Room</TableHead>
                      <TableHead>Teacher</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <TableRow>
                      <TableCell className="font-medium">Mathematics 101</TableCell>
                      <TableCell>08:00 - 09:30</TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          <Badge variant="secondary" className="text-xs">Mon</Badge>
                          <Badge variant="secondary" className="text-xs">Wed</Badge>
                          <Badge variant="secondary" className="text-xs">Fri</Badge>
                        </div>
                      </TableCell>
                      <TableCell>Room 101</TableCell>
                      <TableCell>Prof. Santos</TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          <Button variant="ghost" size="sm">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">English 102</TableCell>
                      <TableCell>10:00 - 11:30</TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          <Badge variant="secondary" className="text-xs">Tue</Badge>
                          <Badge variant="secondary" className="text-xs">Thu</Badge>
                        </div>
                      </TableCell>
                      <TableCell>Room 102</TableCell>
                      <TableCell>Prof. Garcia</TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          <Button variant="ghost" size="sm">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>

                <Button onClick={() => handleSaveSettings("Class Schedules")}>
                  <Save className="h-4 w-4 mr-2" />
                  Save Schedule Configuration
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          {/* SMS Configuration Tab */}
          <TabsContent value="sms" className="space-y-6">
            {/* Semaphore API Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5" />
                  Semaphore API Configuration
                </CardTitle>
                <CardDescription>
                  Configure SMS service settings and API credentials
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <Label htmlFor="apiKey">API Key</Label>
                    <Input
                      id="apiKey"
                      type="password"
                      value={smsSettings.apiKey}
                      onChange={(e) => setSmsSettings(prev => ({ ...prev, apiKey: e.target.value }))}
                      placeholder="Enter your Semaphore API key"
                    />
                  </div>
                  <div>
                    <Label htmlFor="apiSecret">API Secret</Label>
                    <Input
                      id="apiSecret"
                      type="password"
                      value={smsSettings.apiSecret}
                      onChange={(e) => setSmsSettings(prev => ({ ...prev, apiSecret: e.target.value }))}
                      placeholder="Enter your API secret"
                    />
                  </div>
                  <div>
                    <Label htmlFor="senderName">Sender Name</Label>
                    <Input
                      id="senderName"
                      value={smsSettings.senderName}
                      onChange={(e) => setSmsSettings(prev => ({ ...prev, senderName: e.target.value }))}
                      placeholder="TSAT"
                      maxLength={11}
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Maximum 11 characters for sender name
                    </p>
                  </div>
                  <div>
                    <Label htmlFor="dailyLimit">Daily SMS Limit</Label>
                    <Input
                      id="dailyLimit"
                      type="number"
                      value={smsSettings.dailyLimit}
                      onChange={(e) => setSmsSettings(prev => ({ ...prev, dailyLimit: parseInt(e.target.value) }))}
                    />
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">Enable SMS Service</div>
                    <div className="text-sm text-gray-500">Turn on/off SMS notifications</div>
                  </div>
                  <Switch
                    checked={smsSettings.enabled}
                    onCheckedChange={(checked) => setSmsSettings(prev => ({ ...prev, enabled: checked }))}
                  />
                </div>

                <Button onClick={() => handleSaveSettings("SMS API")}>
                  <Save className="h-4 w-4 mr-2" />
                  Save API Settings
                </Button>
              </CardContent>
            </Card>

            {/* Message Templates */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Message Templates
                </CardTitle>
                <CardDescription>
                  Create and manage SMS message templates
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <p className="text-sm text-gray-600">Manage templates for different notification types</p>
                  <Button size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Template
                  </Button>
                </div>

                <div className="space-y-3">
                  <div className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <Badge variant="default">Attendance</Badge>
                        <span className="font-medium">Daily Attendance Alert</span>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">
                      Good day! Your child {"{STUDENT_NAME}"} was marked {"{STATUS}"} today at {"{TIME}"}. - TSAT
                    </p>
                    <div className="flex gap-2 text-xs text-gray-500">
                      <span>Variables: STUDENT_NAME, STATUS, TIME</span>
                    </div>
                  </div>

                  <div className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <Badge variant="destructive">Absence</Badge>
                        <span className="font-medium">Absence Notification</span>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">
                      Your child {"{STUDENT_NAME}"} was absent from class today ({"{DATE}"}). Please contact the school if this is excused. - TSAT
                    </p>
                    <div className="flex gap-2 text-xs text-gray-500">
                      <span>Variables: STUDENT_NAME, DATE</span>
                    </div>
                  </div>
                </div>

                <Button onClick={() => handleSaveSettings("Message Templates")}>
                  <Save className="h-4 w-4 mr-2" />
                  Save Templates
                </Button>
              </CardContent>
            </Card>

            {/* Parent Notification Preferences */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bell className="h-5 w-5" />
                  Notification Preferences
                </CardTitle>
                <CardDescription>
                  Configure who receives SMS notifications
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Parent Notifications</div>
                      <div className="text-sm text-gray-500">Send attendance alerts to parents/guardians</div>
                    </div>
                    <Switch
                      checked={smsSettings.parentNotifications}
                      onCheckedChange={(checked) => setSmsSettings(prev => ({ ...prev, parentNotifications: checked }))}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Teacher Notifications</div>
                      <div className="text-sm text-gray-500">Send notifications to teachers</div>
                    </div>
                    <Switch
                      checked={smsSettings.teacherNotifications}
                      onCheckedChange={(checked) => setSmsSettings(prev => ({ ...prev, teacherNotifications: checked }))}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Admin Notifications</div>
                      <div className="text-sm text-gray-500">Send system alerts to administrators</div>
                    </div>
                    <Switch
                      checked={smsSettings.adminNotifications}
                      onCheckedChange={(checked) => setSmsSettings(prev => ({ ...prev, adminNotifications: checked }))}
                    />
                  </div>
                </div>

                <Button onClick={() => handleSaveSettings("Notification Preferences")}>
                  <Save className="h-4 w-4 mr-2" />
                  Save Preferences
                </Button>
              </CardContent>
            </Card>

            {/* SMS Delivery Status */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Smartphone className="h-5 w-5" />
                  SMS Delivery Monitoring
                </CardTitle>
                <CardDescription>
                  Monitor SMS delivery status and statistics
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-3">
                  <div className="text-center p-4 border rounded-lg">
                    <div className="text-2xl font-bold text-green-600">247</div>
                    <div className="text-sm text-gray-500">Sent Today</div>
                  </div>
                  <div className="text-center p-4 border rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">98.5%</div>
                    <div className="text-sm text-gray-500">Delivery Rate</div>
                  </div>
                  <div className="text-center p-4 border rounded-lg">
                    <div className="text-2xl font-bold text-orange-600">1,847</div>
                    <div className="text-sm text-gray-500">This Month</div>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">Recent SMS Activity</h4>
                  <div className="space-y-2 max-h-40 overflow-y-auto">
                    <div className="flex justify-between items-center text-sm p-2 bg-gray-50 dark:bg-gray-800 rounded">
                      <span>Attendance alert to parent</span>
                      <Badge variant="default" className="text-xs">Delivered</Badge>
                    </div>
                    <div className="flex justify-between items-center text-sm p-2 bg-gray-50 dark:bg-gray-800 rounded">
                      <span>Absence notification</span>
                      <Badge variant="default" className="text-xs">Delivered</Badge>
                    </div>
                    <div className="flex justify-between items-center text-sm p-2 bg-gray-50 dark:bg-gray-800 rounded">
                      <span>Late arrival alert</span>
                      <Badge variant="secondary" className="text-xs">Pending</Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* QR Code Settings Tab */}
          <TabsContent value="qr" className="space-y-6">
            {/* QR Code Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <QrCode className="h-5 w-5" />
                  QR Code Configuration
                </CardTitle>
                <CardDescription>
                  Configure QR code generation and security settings
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <Label htmlFor="expirationTime">Expiration Time (minutes)</Label>
                    <Input
                      id="expirationTime"
                      type="number"
                      value={qrSettings.expirationMinutes}
                      onChange={(e) => setQrSettings(prev => ({ ...prev, expirationMinutes: parseInt(e.target.value) }))}
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      How long QR codes remain valid (0 = never expire)
                    </p>
                  </div>
                  <div>
                    <Label htmlFor="securityLevel">Security Level</Label>
                    <Select value={qrSettings.securityLevel} onValueChange={(value: "low" | "medium" | "high") => setQrSettings(prev => ({ ...prev, securityLevel: value }))}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">Low - Basic validation</SelectItem>
                        <SelectItem value="medium">Medium - Timestamp validation</SelectItem>
                        <SelectItem value="high">High - Encrypted with rotation</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="maxScans">Max Scans Per Code</Label>
                    <Input
                      id="maxScans"
                      type="number"
                      value={qrSettings.maxScansPerCode}
                      onChange={(e) => setQrSettings(prev => ({ ...prev, maxScansPerCode: parseInt(e.target.value) }))}
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Maximum times a QR code can be scanned (0 = unlimited)
                    </p>
                  </div>
                  <div>
                    <Label htmlFor="batchSize">Batch Generation Size</Label>
                    <Input
                      id="batchSize"
                      type="number"
                      value={qrSettings.batchSize}
                      onChange={(e) => setQrSettings(prev => ({ ...prev, batchSize: parseInt(e.target.value) }))}
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Number of QR codes to generate at once
                    </p>
                  </div>
                </div>

                <Separator />

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Regenerate on Scan</div>
                      <div className="text-sm text-gray-500">Generate new QR code after each scan</div>
                    </div>
                    <Switch
                      checked={qrSettings.regenerateOnScan}
                      onCheckedChange={(checked) => setQrSettings(prev => ({ ...prev, regenerateOnScan: checked }))}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Include Student Photo</div>
                      <div className="text-sm text-gray-500">Add student photo to printed QR codes</div>
                    </div>
                    <Switch
                      checked={qrSettings.includeStudentPhoto}
                      onCheckedChange={(checked) => setQrSettings(prev => ({ ...prev, includeStudentPhoto: checked }))}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Include School Logo</div>
                      <div className="text-sm text-gray-500">Add school logo to printed QR codes</div>
                    </div>
                    <Switch
                      checked={qrSettings.includeSchoolLogo}
                      onCheckedChange={(checked) => setQrSettings(prev => ({ ...prev, includeSchoolLogo: checked }))}
                    />
                  </div>
                </div>

                <Button onClick={() => handleSaveSettings("QR Code Configuration")}>
                  <Save className="h-4 w-4 mr-2" />
                  Save QR Settings
                </Button>
              </CardContent>
            </Card>

            {/* Print Layout Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Printer className="h-5 w-5" />
                  Print Layout Settings
                </CardTitle>
                <CardDescription>
                  Configure how QR codes are printed and displayed
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="printLayout">Print Layout</Label>
                  <Select value={qrSettings.printLayout} onValueChange={(value: "grid" | "list" | "cards") => setQrSettings(prev => ({ ...prev, printLayout: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="grid">Grid Layout - Multiple codes per page</SelectItem>
                      <SelectItem value="list">List Layout - One code per row</SelectItem>
                      <SelectItem value="cards">Card Layout - Individual student cards</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid gap-4 md:grid-cols-3">
                  <div className="border rounded-lg p-4 text-center">
                    <div className="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded mx-auto mb-2 flex items-center justify-center">
                      <QrCode className="h-8 w-8" />
                    </div>
                    <p className="text-sm font-medium">Grid Layout</p>
                    <p className="text-xs text-gray-500">6x4 codes per page</p>
                  </div>
                  <div className="border rounded-lg p-4 text-center">
                    <div className="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded mx-auto mb-2 flex items-center justify-center">
                      <QrCode className="h-8 w-8" />
                    </div>
                    <p className="text-sm font-medium">List Layout</p>
                    <p className="text-xs text-gray-500">Detailed information</p>
                  </div>
                  <div className="border rounded-lg p-4 text-center">
                    <div className="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded mx-auto mb-2 flex items-center justify-center">
                      <QrCode className="h-8 w-8" />
                    </div>
                    <p className="text-sm font-medium">Card Layout</p>
                    <p className="text-xs text-gray-500">ID card format</p>
                  </div>
                </div>

                <Button onClick={() => handleSaveSettings("Print Layout")}>
                  <Save className="h-4 w-4 mr-2" />
                  Save Layout Settings
                </Button>
              </CardContent>
            </Card>

            {/* Batch Generation */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <QrCode className="h-5 w-5" />
                  Batch QR Code Generation
                </CardTitle>
                <CardDescription>
                  Generate QR codes for multiple students at once
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <Label htmlFor="selectClass">Select Class/Section</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose class to generate codes" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="grade-11-a">Grade 11-A</SelectItem>
                        <SelectItem value="grade-11-b">Grade 11-B</SelectItem>
                        <SelectItem value="grade-12-a">Grade 12-A</SelectItem>
                        <SelectItem value="grade-12-b">Grade 12-B</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="codeType">Code Type</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select code type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="permanent">Permanent Codes</SelectItem>
                        <SelectItem value="daily">Daily Codes</SelectItem>
                        <SelectItem value="weekly">Weekly Codes</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button className="flex-1">
                    <QrCode className="h-4 w-4 mr-2" />
                    Generate QR Codes
                  </Button>
                  <Button variant="outline">
                    <Eye className="h-4 w-4 mr-2" />
                    Preview
                  </Button>
                  <Button variant="outline">
                    <Printer className="h-4 w-4 mr-2" />
                    Print
                  </Button>
                </div>

                <div className="text-sm text-gray-600 p-3 bg-gray-50 dark:bg-gray-800 rounded">
                  <p className="font-medium mb-1">Last Generation:</p>
                  <p>Grade 11-A - 25 codes generated on March 15, 2024</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* User Management Tab */}
          <TabsContent value="users" className="space-y-6">
            {/* Teacher Accounts */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Teacher Accounts
                </CardTitle>
                <CardDescription>
                  Manage teacher accounts and access permissions
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <p className="text-sm text-gray-600">Manage system users and their roles</p>
                  <Button size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Teacher
                  </Button>
                </div>

                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Role</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Last Login</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <TableRow>
                      <TableCell className="font-medium">Prof. Maria Santos</TableCell>
                      <TableCell><EMAIL></TableCell>
                      <TableCell>
                        <Badge variant="default">Administrator</Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant="default" className="bg-green-100 text-green-800">Active</Badge>
                      </TableCell>
                      <TableCell>2 hours ago</TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          <Button variant="ghost" size="sm">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Key className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">Prof. Juan Garcia</TableCell>
                      <TableCell><EMAIL></TableCell>
                      <TableCell>
                        <Badge variant="secondary">Teacher</Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant="default" className="bg-green-100 text-green-800">Active</Badge>
                      </TableCell>
                      <TableCell>1 day ago</TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          <Button variant="ghost" size="sm">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Key className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">Prof. Ana Reyes</TableCell>
                      <TableCell><EMAIL></TableCell>
                      <TableCell>
                        <Badge variant="secondary">Teacher</Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className="bg-yellow-100 text-yellow-800">Inactive</Badge>
                      </TableCell>
                      <TableCell>1 week ago</TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          <Button variant="ghost" size="sm">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Key className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </CardContent>
            </Card>

            {/* Role Permissions Matrix */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  Role Permissions Matrix
                </CardTitle>
                <CardDescription>
                  Configure permissions for different user roles
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Permission</TableHead>
                      <TableHead className="text-center">Administrator</TableHead>
                      <TableHead className="text-center">Teacher</TableHead>
                      <TableHead className="text-center">Staff</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <TableRow>
                      <TableCell className="font-medium">View Students</TableCell>
                      <TableCell className="text-center">
                        <Switch checked disabled />
                      </TableCell>
                      <TableCell className="text-center">
                        <Switch checked />
                      </TableCell>
                      <TableCell className="text-center">
                        <Switch checked />
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">Edit Students</TableCell>
                      <TableCell className="text-center">
                        <Switch checked disabled />
                      </TableCell>
                      <TableCell className="text-center">
                        <Switch checked />
                      </TableCell>
                      <TableCell className="text-center">
                        <Switch />
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">Scan QR Codes</TableCell>
                      <TableCell className="text-center">
                        <Switch checked disabled />
                      </TableCell>
                      <TableCell className="text-center">
                        <Switch checked />
                      </TableCell>
                      <TableCell className="text-center">
                        <Switch checked />
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">Generate Reports</TableCell>
                      <TableCell className="text-center">
                        <Switch checked disabled />
                      </TableCell>
                      <TableCell className="text-center">
                        <Switch checked />
                      </TableCell>
                      <TableCell className="text-center">
                        <Switch />
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">System Settings</TableCell>
                      <TableCell className="text-center">
                        <Switch checked disabled />
                      </TableCell>
                      <TableCell className="text-center">
                        <Switch />
                      </TableCell>
                      <TableCell className="text-center">
                        <Switch />
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">User Management</TableCell>
                      <TableCell className="text-center">
                        <Switch checked disabled />
                      </TableCell>
                      <TableCell className="text-center">
                        <Switch />
                      </TableCell>
                      <TableCell className="text-center">
                        <Switch />
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>

                <Button onClick={() => handleSaveSettings("Role Permissions")}>
                  <Save className="h-4 w-4 mr-2" />
                  Save Permissions
                </Button>
              </CardContent>
            </Card>

            {/* Password Policies */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Key className="h-5 w-5" />
                  Password Policies
                </CardTitle>
                <CardDescription>
                  Configure password requirements and security policies
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <Label htmlFor="minLength">Minimum Length</Label>
                    <Input
                      id="minLength"
                      type="number"
                      defaultValue="8"
                      min="6"
                      max="20"
                    />
                  </div>
                  <div>
                    <Label htmlFor="maxAge">Password Max Age (days)</Label>
                    <Input
                      id="maxAge"
                      type="number"
                      defaultValue="90"
                      min="30"
                      max="365"
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Require Uppercase Letters</div>
                      <div className="text-sm text-gray-500">At least one uppercase letter (A-Z)</div>
                    </div>
                    <Switch defaultChecked />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Require Numbers</div>
                      <div className="text-sm text-gray-500">At least one number (0-9)</div>
                    </div>
                    <Switch defaultChecked />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Require Special Characters</div>
                      <div className="text-sm text-gray-500">At least one special character (!@#$%)</div>
                    </div>
                    <Switch />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Prevent Password Reuse</div>
                      <div className="text-sm text-gray-500">Remember last 5 passwords</div>
                    </div>
                    <Switch defaultChecked />
                  </div>
                </div>

                <Button onClick={() => handleSaveSettings("Password Policies")}>
                  <Save className="h-4 w-4 mr-2" />
                  Save Password Policies
                </Button>
              </CardContent>
            </Card>

            {/* Session Management */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Monitor className="h-5 w-5" />
                  Session Management
                </CardTitle>
                <CardDescription>
                  Configure user session settings and security
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <Label htmlFor="sessionDuration">Max Session Duration (minutes)</Label>
                    <Input
                      id="sessionDuration"
                      type="number"
                      defaultValue="480"
                      min="30"
                      max="1440"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Maximum time before automatic logout
                    </p>
                  </div>
                  <div>
                    <Label htmlFor="warningTime">Warning Time (minutes)</Label>
                    <Input
                      id="warningTime"
                      type="number"
                      defaultValue="15"
                      min="5"
                      max="60"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Show warning before session expires
                    </p>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Allow Multiple Sessions</div>
                      <div className="text-sm text-gray-500">Users can login from multiple devices</div>
                    </div>
                    <Switch defaultChecked />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Track Login Location</div>
                      <div className="text-sm text-gray-500">Log IP addresses and locations</div>
                    </div>
                    <Switch defaultChecked />
                  </div>
                </div>

                <Button onClick={() => handleSaveSettings("Session Management")}>
                  <Save className="h-4 w-4 mr-2" />
                  Save Session Settings
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          {/* System Backup Tab */}
          <TabsContent value="backup" className="space-y-6">
            {/* Automated Backup Scheduling */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="h-5 w-5" />
                  Automated Backup Scheduling
                </CardTitle>
                <CardDescription>
                  Configure automatic backup schedules and retention policies
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-3">
                  <div>
                    <Label htmlFor="backupFrequency">Backup Frequency</Label>
                    <Select value={backupSettings.backupFrequency} onValueChange={(value: "daily" | "weekly" | "monthly") => setBackupSettings(prev => ({ ...prev, backupFrequency: value }))}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="daily">Daily</SelectItem>
                        <SelectItem value="weekly">Weekly</SelectItem>
                        <SelectItem value="monthly">Monthly</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="backupTime">Backup Time</Label>
                    <Input
                      id="backupTime"
                      type="time"
                      value={backupSettings.backupTime}
                      onChange={(e) => setBackupSettings(prev => ({ ...prev, backupTime: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="retentionDays">Retention Period (days)</Label>
                    <Input
                      id="retentionDays"
                      type="number"
                      value={backupSettings.retentionDays}
                      onChange={(e) => setBackupSettings(prev => ({ ...prev, retentionDays: parseInt(e.target.value) }))}
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Enable Auto Backup</div>
                      <div className="text-sm text-gray-500">Automatically backup database on schedule</div>
                    </div>
                    <Switch
                      checked={backupSettings.autoBackupEnabled}
                      onCheckedChange={(checked) => setBackupSettings(prev => ({ ...prev, autoBackupEnabled: checked }))}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Include Attachments</div>
                      <div className="text-sm text-gray-500">Include uploaded files and images in backup</div>
                    </div>
                    <Switch
                      checked={backupSettings.includeAttachments}
                      onCheckedChange={(checked) => setBackupSettings(prev => ({ ...prev, includeAttachments: checked }))}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Cloud Backup</div>
                      <div className="text-sm text-gray-500">Store backups in cloud storage</div>
                    </div>
                    <Switch
                      checked={backupSettings.cloudBackupEnabled}
                      onCheckedChange={(checked) => setBackupSettings(prev => ({ ...prev, cloudBackupEnabled: checked }))}
                    />
                  </div>
                </div>

                <Button onClick={() => handleSaveSettings("Backup Schedule")}>
                  <Save className="h-4 w-4 mr-2" />
                  Save Backup Settings
                </Button>
              </CardContent>
            </Card>

            {/* Manual Backup & Restore */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="h-5 w-5" />
                  Manual Backup & Restore
                </CardTitle>
                <CardDescription>
                  Create manual backups and restore from previous backups
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                    <h3 className="font-medium mb-2">Create Manual Backup</h3>
                    <p className="text-sm text-gray-500 mb-3">
                      Generate a backup of the current database state
                    </p>
                    <Button className="w-full">
                      <Database className="h-4 w-4 mr-2" />
                      Create Backup Now
                    </Button>
                  </div>
                  <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                    <h3 className="font-medium mb-2">Restore from Backup</h3>
                    <p className="text-sm text-gray-500 mb-3">
                      Restore database from a previous backup
                    </p>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="outline" className="w-full">
                          <Upload className="h-4 w-4 mr-2" />
                          Restore Backup
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Restore Database</AlertDialogTitle>
                          <AlertDialogDescription>
                            This action will replace all current data with the backup data. This cannot be undone.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction>Restore</AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">Recent Backups</h4>
                  <div className="space-y-2 max-h-40 overflow-y-auto">
                    <div className="flex justify-between items-center text-sm p-3 border rounded">
                      <div>
                        <div className="font-medium">backup_2024-03-15_03-00.sql</div>
                        <div className="text-gray-500">March 15, 2024 at 3:00 AM • 2.4 MB</div>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="ghost" size="sm">
                          <Download className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Upload className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <div className="flex justify-between items-center text-sm p-3 border rounded">
                      <div>
                        <div className="font-medium">backup_2024-03-14_03-00.sql</div>
                        <div className="text-gray-500">March 14, 2024 at 3:00 AM • 2.3 MB</div>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="ghost" size="sm">
                          <Download className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Upload className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Data Export */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Download className="h-5 w-5" />
                  Data Export Functionality
                </CardTitle>
                <CardDescription>
                  Export specific data sets for analysis or migration
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <Label htmlFor="exportType">Export Type</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select data to export" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Data</SelectItem>
                        <SelectItem value="students">Students Only</SelectItem>
                        <SelectItem value="attendance">Attendance Records</SelectItem>
                        <SelectItem value="users">User Accounts</SelectItem>
                        <SelectItem value="settings">System Settings</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="exportFormat">Export Format</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select format" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="csv">CSV</SelectItem>
                        <SelectItem value="excel">Excel (XLSX)</SelectItem>
                        <SelectItem value="json">JSON</SelectItem>
                        <SelectItem value="pdf">PDF Report</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <Label htmlFor="dateFrom">Date From</Label>
                    <Input
                      id="dateFrom"
                      type="date"
                    />
                  </div>
                  <div>
                    <Label htmlFor="dateTo">Date To</Label>
                    <Input
                      id="dateTo"
                      type="date"
                    />
                  </div>
                </div>

                <Button className="w-full">
                  <Download className="h-4 w-4 mr-2" />
                  Export Data
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Appearance Tab */}
          <TabsContent value="appearance" className="space-y-6">
            {/* Theme Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Palette className="h-5 w-5" />
                  Theme & Appearance
                </CardTitle>
                <CardDescription>
                  Customize the look and feel of the application
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-3">
                  <div>
                    <Label htmlFor="theme">Theme Mode</Label>
                    <Select value={appearanceSettings.theme} onValueChange={(value: "light" | "dark" | "system") => setAppearanceSettings(prev => ({ ...prev, theme: value }))}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="system">System</SelectItem>
                        <SelectItem value="light">Light</SelectItem>
                        <SelectItem value="dark">Dark</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="fontSize">Font Size</Label>
                    <Select value={appearanceSettings.fontSize} onValueChange={(value: "small" | "medium" | "large") => setAppearanceSettings(prev => ({ ...prev, fontSize: value }))}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="small">Small</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="large">Large</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="primaryColor">Primary Color</Label>
                    <Input
                      id="primaryColor"
                      type="color"
                      value={appearanceSettings.primaryColor}
                      onChange={(e) => setAppearanceSettings(prev => ({ ...prev, primaryColor: e.target.value }))}
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Compact Mode</div>
                      <div className="text-sm text-gray-500">Reduce spacing and padding for more content</div>
                    </div>
                    <Switch
                      checked={appearanceSettings.compactMode}
                      onCheckedChange={(checked) => setAppearanceSettings(prev => ({ ...prev, compactMode: checked }))}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Show Animations</div>
                      <div className="text-sm text-gray-500">Enable smooth transitions and animations</div>
                    </div>
                    <Switch
                      checked={appearanceSettings.showAnimations}
                      onCheckedChange={(checked) => setAppearanceSettings(prev => ({ ...prev, showAnimations: checked }))}
                    />
                  </div>
                </div>

                <Button onClick={() => handleSaveSettings("Appearance")}>
                  <Save className="h-4 w-4 mr-2" />
                  Save Appearance Settings
                </Button>
              </CardContent>
            </Card>

            {/* Custom Branding */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <School className="h-5 w-5" />
                  Custom Branding
                </CardTitle>
                <CardDescription>
                  Upload custom logos and branding elements
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label>School Logo</Label>
                    <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center">
                      <School className="h-12 w-12 mx-auto text-gray-400 mb-2" />
                      <p className="text-sm text-gray-500 mb-2">Upload school logo</p>
                      <Button variant="outline" size="sm">
                        <Upload className="h-4 w-4 mr-2" />
                        Choose File
                      </Button>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>Favicon</Label>
                    <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center">
                      <Globe className="h-12 w-12 mx-auto text-gray-400 mb-2" />
                      <p className="text-sm text-gray-500 mb-2">Upload favicon (16x16)</p>
                      <Button variant="outline" size="sm">
                        <Upload className="h-4 w-4 mr-2" />
                        Choose File
                      </Button>
                    </div>
                  </div>
                </div>

                <Button onClick={() => handleSaveSettings("Custom Branding")}>
                  <Save className="h-4 w-4 mr-2" />
                  Save Branding
                </Button>
              </CardContent>
            </Card>

            {/* Preview */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  Theme Preview
                </CardTitle>
                <CardDescription>
                  Preview how your settings will look
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="border rounded-lg p-4 bg-gray-50 dark:bg-gray-800">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="font-semibold">Sample Dashboard</h3>
                    <Badge>Preview</Badge>
                  </div>
                  <div className="grid gap-3 md:grid-cols-3">
                    <div className="bg-white dark:bg-gray-700 p-3 rounded shadow-sm">
                      <div className="text-sm text-gray-500">Total Students</div>
                      <div className="text-2xl font-bold">1,247</div>
                    </div>
                    <div className="bg-white dark:bg-gray-700 p-3 rounded shadow-sm">
                      <div className="text-sm text-gray-500">Present Today</div>
                      <div className="text-2xl font-bold text-green-600">1,189</div>
                    </div>
                    <div className="bg-white dark:bg-gray-700 p-3 rounded shadow-sm">
                      <div className="text-sm text-gray-500">Attendance Rate</div>
                      <div className="text-2xl font-bold text-blue-600">95.3%</div>
                    </div>
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button variant="outline">
                    <Eye className="h-4 w-4 mr-2" />
                    Preview Changes
                  </Button>
                  <Button variant="outline">
                    Reset to Default
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  )
}
