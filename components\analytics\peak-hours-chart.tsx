"use client"

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts'

const mockPeakHoursData = [
  { hour: '7:00 AM', count: 234, percentage: 18.5 },
  { hour: '8:00 AM', count: 567, percentage: 44.8 },
  { hour: '9:00 AM', count: 298, percentage: 23.5 },
  { hour: '10:00 AM', count: 123, percentage: 9.7 },
  { hour: '11:00 AM', count: 45, percentage: 3.5 }
]

export function PeakHoursChart() {
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      return (
        <div className="bg-white dark:bg-gray-800 p-3 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg">
          <p className="font-medium">{label}</p>
          <p className="text-sm text-blue-600">
            Check-ins: {data.count} students
          </p>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Percentage: {data.percentage}%
          </p>
        </div>
      )
    }
    return null
  }

  return (
    <div className="h-[300px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={mockPeakHoursData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" className="stroke-gray-200 dark:stroke-gray-700" />
          <XAxis 
            dataKey="hour" 
            className="text-xs fill-gray-600 dark:fill-gray-400"
          />
          <YAxis className="text-xs fill-gray-600 dark:fill-gray-400" />
          <Tooltip content={<CustomTooltip />} />
          <Bar 
            dataKey="count" 
            fill="#3b82f6" 
            radius={[4, 4, 0, 0]}
          />
        </BarChart>
      </ResponsiveContainer>
    </div>
  )
}
