"use client"

import { useState } from "react"
import { MainLayout } from "@/components/layout/main-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DateRangePicker } from "@/components/ui/date-picker"
import {
  BarChart3,
  TrendingUp,
  Calendar,
  Users,
  Clock,
  Target,
  Download,
  Filter,
  AlertTriangle,
  Award,
  Brain,
  Eye,
  RefreshCw
} from "lucide-react"
import { AttendanceTrendChart } from "@/components/analytics/attendance-trend-chart"
import { GradeAttendanceChart } from "@/components/analytics/grade-attendance-chart"
import { AttendanceStatusChart } from "@/components/analytics/attendance-status-chart"
import { PeakHoursC<PERSON> } from "@/components/analytics/peak-hours-chart"
import { AIInsightsPanel } from "@/components/analytics/ai-insights-panel"
import { AttendanceHeatmap } from "@/components/analytics/attendance-heatmap"
import { FilterDialog } from "@/components/analytics/filter-dialog"
import { exportChartData, filterAttendanceData, filterGradeData } from "@/lib/analytics-utils"
import type {
  TimePeriod,
  TimePeriodFilter,
  AnalyticsKPI,
  AnalyticsFilter,
  AttendanceTrendPoint,
  GradeAttendanceData,
  AttendanceStatusDistribution,
  HeatmapData,
  AtRiskStudentDetailed,
  AIInsight,
  PredictiveAnalytics,
  TopPerformer
} from "@/types"

// Mock data for the analytics dashboard
const mockKPIs: AnalyticsKPI[] = [
  {
    id: "overall-attendance",
    title: "Overall Attendance Rate",
    value: "89.2%",
    previousValue: "87.1%",
    change: 2.1,
    changeType: "increase",
    format: "percentage",
    icon: "target",
    color: "green"
  },
  {
    id: "daily-average",
    title: "Daily Average Students",
    value: 1089,
    previousValue: 1045,
    change: 4.2,
    changeType: "increase",
    format: "number",
    icon: "users",
    color: "blue"
  },
  {
    id: "peak-time",
    title: "Peak Attendance Hour",
    value: "8:15 AM",
    previousValue: "8:30 AM",
    format: "time",
    icon: "clock",
    color: "purple"
  },
  {
    id: "at-risk",
    title: "At-Risk Students",
    value: 23,
    previousValue: 31,
    change: -25.8,
    changeType: "decrease",
    format: "number",
    icon: "alert-triangle",
    color: "red"
  }
]

const mockTrendData: AttendanceTrendPoint[] = [
  { date: "2024-01-15", present: 1089, absent: 145, late: 23, excused: 12, total: 1269, attendanceRate: 85.8 },
  { date: "2024-01-16", present: 1156, absent: 98, late: 15, excused: 8, total: 1277, attendanceRate: 90.5 },
  { date: "2024-01-17", present: 1203, absent: 67, late: 18, excused: 5, total: 1293, attendanceRate: 93.0 },
  { date: "2024-01-18", present: 1178, absent: 89, late: 21, excused: 7, total: 1295, attendanceRate: 91.0 },
  { date: "2024-01-19", present: 1134, absent: 123, late: 28, excused: 15, total: 1300, attendanceRate: 87.2 },
  { date: "2024-01-22", present: 1167, absent: 98, late: 19, excused: 11, total: 1295, attendanceRate: 90.1 },
  { date: "2024-01-23", present: 1189, absent: 76, late: 16, excused: 9, total: 1290, attendanceRate: 92.2 }
]

const mockGradeData: GradeAttendanceData[] = [
  { grade: "BSIT", section: "All Sections", totalStudents: 456, presentStudents: 421, absentStudents: 35, lateStudents: 12, attendanceRate: 92.3, trend: 2.1 },
  { grade: "BSBA", section: "All Sections", totalStudents: 389, presentStudents: 342, absentStudents: 47, lateStudents: 18, attendanceRate: 87.9, trend: -1.2 },
  { grade: "BSED", section: "All Sections", totalStudents: 234, presentStudents: 220, absentStudents: 14, lateStudents: 8, attendanceRate: 94.0, trend: 3.5 },
  { grade: "BSCS", section: "All Sections", totalStudents: 155, presentStudents: 139, absentStudents: 16, lateStudents: 5, attendanceRate: 89.7, trend: 0.8 }
]

const mockStatusDistribution: AttendanceStatusDistribution[] = [
  { status: "present", count: 1089, percentage: 85.8, color: "#22c55e" },
  { status: "absent", count: 145, percentage: 11.4, color: "#ef4444" },
  { status: "late", count: 23, percentage: 1.8, color: "#f59e0b" },
  { status: "excused", count: 12, percentage: 0.9, color: "#8b5cf6" }
]

const mockAtRiskStudents: AtRiskStudentDetailed[] = [
  {
    id: "1",
    name: "John Doe",
    studentId: "TSAT-2024-001",
    course: "BSIT",
    attendanceRate: 45.2,
    consecutiveAbsences: 5,
    lastAttendance: "2024-01-10",
    riskLevel: "high",
    trends: mockTrendData.slice(0, 3),
    patterns: [
      { type: "chronic_absence", description: "Frequently absent on Mondays", frequency: 80, severity: "high", detected: new Date("2024-01-15") }
    ],
    recommendations: ["Schedule counseling session", "Contact parent/guardian", "Academic support program"],
    interventions: []
  }
]

const mockAIInsights: AIInsight[] = [
  {
    id: "1",
    type: "prediction",
    title: "Attendance Drop Predicted",
    description: "Based on weather patterns and historical data, attendance may drop by 15% next Friday due to expected heavy rain.",
    confidence: 87,
    impact: "medium",
    category: "attendance",
    actionable: true,
    actions: ["Send weather advisory", "Prepare online backup classes"],
    createdAt: new Date("2024-01-20")
  },
  {
    id: "2",
    type: "pattern",
    title: "Monday Morning Pattern",
    description: "Students in BSBA program show 23% higher absence rate on Monday mornings compared to other days.",
    confidence: 94,
    impact: "high",
    category: "behavior",
    actionable: true,
    actions: ["Investigate scheduling conflicts", "Consider Monday morning interventions"],
    createdAt: new Date("2024-01-19")
  }
]

const mockTopPerformers: TopPerformer[] = [
  {
    id: "1",
    name: "Maria Santos",
    studentId: "TSAT-2024-456",
    course: "BSED",
    section: "ED-3A",
    attendanceRate: 100,
    streak: 45,
    achievements: ["Perfect Attendance", "Early Bird", "Consistent Performer"]
  },
  {
    id: "2",
    name: "Carlos Rodriguez",
    studentId: "TSAT-2024-789",
    course: "BSIT",
    section: "IT-2B",
    attendanceRate: 98.5,
    streak: 32,
    achievements: ["Nearly Perfect", "Reliable Student"]
  }
]

export default function AnalyticsPage() {
  const [timePeriod, setTimePeriod] = useState<TimePeriod>('weekly')
  const [dateRange, setDateRange] = useState<{ from: Date | undefined; to: Date | undefined }>({
    from: undefined,
    to: undefined
  })
  const [filters, setFilters] = useState<AnalyticsFilter>({
    courses: [],
    sections: [],
    yearLevels: [],
    attendanceStatus: [],
    riskLevels: [],
    dateRange: {
      period: 'weekly',
      startDate: undefined,
      endDate: undefined
    }
  })

  // Apply filters to data
  const filteredTrendData = filterAttendanceData(mockTrendData, filters)
  const filteredGradeData = filterGradeData(mockGradeData, filters)

  // Export handlers
  const handleExportTrendData = () => {
    exportChartData(
      'line-chart',
      'Attendance Trends Over Time',
      filteredTrendData,
      filters,
      {
        period: timePeriod,
        customRange: dateRange
      }
    )
  }

  const handleExportGradeData = () => {
    exportChartData(
      'bar-chart',
      'Attendance by Grade Section',
      filteredGradeData,
      filters,
      {
        period: timePeriod,
        customRange: dateRange
      }
    )
  }

  const handleExportStatusData = () => {
    exportChartData(
      'pie-chart',
      'Attendance Status Distribution',
      mockStatusDistribution,
      filters,
      {
        period: timePeriod,
        customRange: dateRange
      }
    )
  }

  return (
    <MainLayout title="Analytics">
      <div className="space-y-6">
        {/* Header with Controls */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              Attendance Analytics Dashboard
            </h2>
            <p className="text-gray-600 dark:text-gray-400">
              Comprehensive insights with AI-powered analytics and predictions
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Select value={timePeriod} onValueChange={(value: TimePeriod) => setTimePeriod(value)}>
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="Time Period" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="daily">Daily</SelectItem>
                <SelectItem value="weekly">Weekly</SelectItem>
                <SelectItem value="monthly">Monthly</SelectItem>
                <SelectItem value="custom">Custom</SelectItem>
              </SelectContent>
            </Select>
            {timePeriod === 'custom' && (
              <DateRangePicker
                dateRange={dateRange}
                onDateRangeChange={setDateRange}
                placeholder="Select date range"
                className="w-[280px]"
              />
            )}
            <FilterDialog
              filters={filters}
              onFiltersChange={setFilters}
            />
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                // Export all data as a combined report
                const combinedData = {
                  trends: filteredTrendData,
                  grades: filteredGradeData,
                  status: mockStatusDistribution,
                  kpis: mockKPIs
                }
                exportChartData(
                  'combined-report',
                  'Complete Analytics Report',
                  [combinedData],
                  filters,
                  {
                    period: timePeriod,
                    customRange: dateRange
                  }
                )
              }}
            >
              <Download className="h-4 w-4 mr-2" />
              Export All
            </Button>
            <Button variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Key Performance Indicators */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {mockKPIs.map((kpi) => {
            const IconComponent = kpi.icon === 'target' ? Target :
                                 kpi.icon === 'users' ? Users :
                                 kpi.icon === 'clock' ? Clock :
                                 kpi.icon === 'alert-triangle' ? AlertTriangle : Target

            const colorClass = kpi.color === 'green' ? 'text-green-600' :
                              kpi.color === 'blue' ? 'text-blue-600' :
                              kpi.color === 'purple' ? 'text-purple-600' :
                              kpi.color === 'red' ? 'text-red-600' : 'text-gray-600'

            return (
              <Card key={kpi.id} className="hover:shadow-md transition-shadow">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">{kpi.title}</CardTitle>
                  <IconComponent className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className={`text-2xl font-bold ${colorClass}`}>
                    {kpi.value}
                  </div>
                  {kpi.change && (
                    <p className="text-xs text-muted-foreground flex items-center gap-1">
                      {kpi.changeType === 'increase' ? (
                        <TrendingUp className="h-3 w-3 text-green-500" />
                      ) : (
                        <TrendingUp className="h-3 w-3 text-red-500 rotate-180" />
                      )}
                      {kpi.changeType === 'increase' ? '+' : ''}{kpi.change}% from previous period
                    </p>
                  )}
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Interactive Charts Section */}
        <Tabs defaultValue="trends" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="trends">Trends</TabsTrigger>
            <TabsTrigger value="distribution">Distribution</TabsTrigger>
            <TabsTrigger value="patterns">Patterns</TabsTrigger>
            <TabsTrigger value="heatmap">Heatmap</TabsTrigger>
          </TabsList>

          <TabsContent value="trends" className="space-y-6">
            <div className="grid gap-6 lg:grid-cols-2">
              {/* Attendance Trend Line Chart */}
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="flex items-center gap-2">
                        <TrendingUp className="h-5 w-5" />
                        Attendance Trends Over Time
                      </CardTitle>
                      <CardDescription>
                        Daily attendance rates and patterns
                      </CardDescription>
                    </div>
                    <Button variant="outline" size="sm" onClick={handleExportTrendData}>
                      <Download className="h-4 w-4 mr-2" />
                      Export
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <AttendanceTrendChart data={filteredTrendData} />
                </CardContent>
              </Card>

              {/* Grade/Section Bar Chart */}
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="flex items-center gap-2">
                        <BarChart3 className="h-5 w-5" />
                        Attendance by Grade/Section
                      </CardTitle>
                      <CardDescription>
                        Comparative attendance rates across programs
                      </CardDescription>
                    </div>
                    <Button variant="outline" size="sm" onClick={handleExportGradeData}>
                      <Download className="h-4 w-4 mr-2" />
                      Export
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <GradeAttendanceChart data={filteredGradeData} />
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="distribution" className="space-y-6">
            <div className="grid gap-6 lg:grid-cols-2">
              {/* Status Distribution Pie Chart */}
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="flex items-center gap-2">
                        <Target className="h-5 w-5" />
                        Attendance Status Distribution
                      </CardTitle>
                      <CardDescription>
                        Breakdown of attendance statuses
                      </CardDescription>
                    </div>
                    <Button variant="outline" size="sm" onClick={handleExportStatusData}>
                      <Download className="h-4 w-4 mr-2" />
                      Export
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <AttendanceStatusChart data={mockStatusDistribution} />
                </CardContent>
              </Card>

              {/* Peak Hours Chart */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Clock className="h-5 w-5" />
                    Peak Attendance Hours
                  </CardTitle>
                  <CardDescription>
                    Check-in time distribution throughout the day
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <PeakHoursChart />
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="patterns" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Brain className="h-5 w-5" />
                  AI-Powered Pattern Analysis
                </CardTitle>
                <CardDescription>
                  Intelligent insights and pattern recognition
                </CardDescription>
              </CardHeader>
              <CardContent>
                <AIInsightsPanel insights={mockAIInsights} />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="heatmap" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Daily Attendance Heatmap
                </CardTitle>
                <CardDescription>
                  Visual representation of attendance patterns by day and time
                </CardDescription>
              </CardHeader>
              <CardContent>
                <AttendanceHeatmap />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* At-Risk Students and Performance Analysis */}
        <div className="grid gap-6 lg:grid-cols-2">
          {/* At-Risk Students Panel */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-red-500" />
                At-Risk Students
              </CardTitle>
              <CardDescription>
                Students requiring immediate attention and intervention
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockAtRiskStudents.map((student) => (
                  <div key={student.id} className="p-4 border border-red-200 dark:border-red-800 rounded-lg bg-red-50 dark:bg-red-900/20">
                    <div className="flex items-start justify-between mb-2">
                      <div>
                        <h4 className="font-medium">{student.name}</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {student.studentId} • {student.course}
                        </p>
                      </div>
                      <Badge variant="destructive" className="text-xs">
                        {student.riskLevel} risk
                      </Badge>
                    </div>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600 dark:text-gray-400">Attendance Rate:</span>
                        <span className="ml-2 font-medium text-red-600">{student.attendanceRate}%</span>
                      </div>
                      <div>
                        <span className="text-gray-600 dark:text-gray-400">Consecutive Absences:</span>
                        <span className="ml-2 font-medium">{student.consecutiveAbsences}</span>
                      </div>
                    </div>
                    <div className="mt-3">
                      <p className="text-xs text-gray-600 dark:text-gray-400 mb-2">Recommendations:</p>
                      <div className="flex flex-wrap gap-1">
                        {student.recommendations.slice(0, 2).map((rec, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {rec}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <div className="flex gap-2 mt-3">
                      <Button size="sm" variant="outline" className="text-xs">
                        View Details
                      </Button>
                      <Button size="sm" className="text-xs">
                        Create Intervention
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Top Performers Panel */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="h-5 w-5 text-yellow-500" />
                Top Performers
              </CardTitle>
              <CardDescription>
                Students with exceptional attendance records
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockTopPerformers.map((student) => (
                  <div key={student.id} className="p-4 border border-green-200 dark:border-green-800 rounded-lg bg-green-50 dark:bg-green-900/20">
                    <div className="flex items-start justify-between mb-2">
                      <div>
                        <h4 className="font-medium">{student.name}</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {student.studentId} • {student.course} - {student.section}
                        </p>
                      </div>
                      <Badge variant="default" className="text-xs bg-green-600">
                        {student.attendanceRate}%
                      </Badge>
                    </div>
                    <div className="grid grid-cols-2 gap-4 text-sm mb-3">
                      <div>
                        <span className="text-gray-600 dark:text-gray-400">Perfect Days:</span>
                        <span className="ml-2 font-medium text-green-600">{student.streak}</span>
                      </div>
                      <div>
                        <span className="text-gray-600 dark:text-gray-400">Achievements:</span>
                        <span className="ml-2 font-medium">{student.achievements.length}</span>
                      </div>
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {student.achievements.slice(0, 2).map((achievement, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {achievement}
                        </Badge>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Predictive Analytics Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Brain className="h-5 w-5 text-purple-500" />
              Predictive Analytics & Forecasting
            </CardTitle>
            <CardDescription>
              AI-powered predictions and trend forecasting
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6 md:grid-cols-3">
              <div className="space-y-3">
                <h4 className="font-medium text-sm">Next Week Prediction</h4>
                <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">87.5%</div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Expected attendance rate</p>
                  <div className="flex items-center gap-1 mt-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-xs text-gray-500">92% confidence</span>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <h4 className="font-medium text-sm">Risk Predictions</h4>
                <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                  <div className="text-2xl font-bold text-yellow-600">5</div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">New at-risk students</p>
                  <div className="flex items-center gap-1 mt-2">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                    <span className="text-xs text-gray-500">Early intervention needed</span>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <h4 className="font-medium text-sm">Improvement Trend</h4>
                <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">12</div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Students improving</p>
                  <div className="flex items-center gap-1 mt-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-xs text-gray-500">Positive trajectory</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">Factors Influencing Predictions</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Key variables considered in forecasting</p>
                </div>
                <Button variant="outline" size="sm">
                  <Eye className="h-4 w-4 mr-2" />
                  View Model Details
                </Button>
              </div>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
                <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="text-sm font-medium">Weather</div>
                  <div className="text-xs text-gray-500">15% impact</div>
                </div>
                <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="text-sm font-medium">Day of Week</div>
                  <div className="text-xs text-gray-500">25% impact</div>
                </div>
                <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="text-sm font-medium">Historical</div>
                  <div className="text-xs text-gray-500">40% impact</div>
                </div>
                <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="text-sm font-medium">Events</div>
                  <div className="text-xs text-gray-500">20% impact</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
