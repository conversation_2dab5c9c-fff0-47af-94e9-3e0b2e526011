import type { 
  AttendanceTrendPoint, 
  GradeAttendanceData, 
  AttendanceStatusDistribution,
  AnalyticsFilter,
  ChartExportData,
  TimePeriodFilter
} from '@/types'

// Export functionality
export const exportChartData = (
  chartType: string,
  title: string,
  data: any[],
  filters: AnalyticsFilter,
  period: TimePeriodFilter
): void => {
  const exportData: ChartExportData = {
    chartType,
    title,
    data,
    metadata: {
      generatedAt: new Date(),
      filters,
      period
    }
  }

  // Convert to CSV format
  const csvContent = convertToCSV(data, chartType)
  downloadCSV(csvContent, `${title.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.csv`)
}

const convertToCSV = (data: any[], chartType: string): string => {
  if (!data || data.length === 0) return ''

  const headers = Object.keys(data[0])
  const csvRows = [
    headers.join(','),
    ...data.map(row => 
      headers.map(header => {
        const value = row[header]
        // Handle values that might contain commas
        return typeof value === 'string' && value.includes(',') 
          ? `"${value}"` 
          : value
      }).join(',')
    )
  ]

  return csvRows.join('\n')
}

const downloadCSV = (csvContent: string, filename: string): void => {
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', filename)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
}

// Data filtering functions
export const filterAttendanceData = (
  data: AttendanceTrendPoint[],
  filters: AnalyticsFilter
): AttendanceTrendPoint[] => {
  return data.filter(point => {
    // Date range filtering
    if (filters.dateRange.startDate && filters.dateRange.endDate) {
      const pointDate = new Date(point.date)
      if (pointDate < filters.dateRange.startDate || pointDate > filters.dateRange.endDate) {
        return false
      }
    }

    return true
  })
}

export const filterGradeData = (
  data: GradeAttendanceData[],
  filters: AnalyticsFilter
): GradeAttendanceData[] => {
  return data.filter(grade => {
    // Course filtering
    if (filters.courses.length > 0 && !filters.courses.includes(grade.grade)) {
      return false
    }

    return true
  })
}

// Drill-down functionality
export const getDrillDownData = (
  level: 'overview' | 'course' | 'section' | 'student',
  context: { courseId?: string; sectionId?: string; studentId?: string },
  baseData: any[]
) => {
  switch (level) {
    case 'course':
      return baseData.filter(item => item.courseId === context.courseId)
    case 'section':
      return baseData.filter(item => 
        item.courseId === context.courseId && item.sectionId === context.sectionId
      )
    case 'student':
      return baseData.filter(item => item.studentId === context.studentId)
    default:
      return baseData
  }
}

// Chart interaction handlers
export const handleChartClick = (
  data: any,
  chartType: string,
  onDrillDown?: (data: any) => void
) => {
  if (onDrillDown) {
    onDrillDown({
      type: chartType,
      data,
      timestamp: new Date()
    })
  }
}

// Date range utilities
export const getDateRangeFromPeriod = (period: TimePeriodFilter): { start: Date; end: Date } => {
  const now = new Date()
  const start = new Date()
  
  switch (period.period) {
    case 'daily':
      start.setDate(now.getDate() - 7) // Last 7 days
      break
    case 'weekly':
      start.setDate(now.getDate() - 30) // Last 30 days
      break
    case 'monthly':
      start.setMonth(now.getMonth() - 6) // Last 6 months
      break
    case 'custom':
      return {
        start: period.customRange?.from || start,
        end: period.customRange?.to || now
      }
    default:
      start.setDate(now.getDate() - 30)
  }
  
  return { start, end: now }
}

// Data aggregation utilities
export const aggregateAttendanceByPeriod = (
  data: AttendanceTrendPoint[],
  period: 'daily' | 'weekly' | 'monthly'
): AttendanceTrendPoint[] => {
  // Implementation would depend on specific aggregation needs
  // This is a simplified version
  return data
}

// Statistical calculations
export const calculateTrends = (data: number[]): { trend: number; direction: 'up' | 'down' | 'stable' } => {
  if (data.length < 2) return { trend: 0, direction: 'stable' }
  
  const recent = data.slice(-3).reduce((a, b) => a + b, 0) / 3
  const previous = data.slice(-6, -3).reduce((a, b) => a + b, 0) / 3
  
  const trend = ((recent - previous) / previous) * 100
  
  return {
    trend: Math.round(trend * 10) / 10,
    direction: trend > 2 ? 'up' : trend < -2 ? 'down' : 'stable'
  }
}
