"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { DateRangePicker } from "@/components/ui/date-picker"
import { Filter, X } from "lucide-react"
import type { AnalyticsFilter, AttendanceStatus } from "@/types"

interface FilterDialogProps {
  filters: AnalyticsFilter
  onFiltersChange: (filters: AnalyticsFilter) => void
}

const availableCourses = ["BSIT", "BSBA", "BSED", "BSCS", "BSHRM"]
const availableSections = ["A", "B", "C", "D"]
const availableYearLevels = [1, 2, 3, 4]
const availableStatuses: AttendanceStatus[] = ["present", "absent", "late", "excused"]

export function FilterDialog({ filters, onFiltersChange }: FilterDialogProps) {
  const [open, setOpen] = useState(false)
  const [tempFilters, setTempFilters] = useState<AnalyticsFilter>(filters)

  const handleApplyFilters = () => {
    onFiltersChange(tempFilters)
    setOpen(false)
  }

  const handleResetFilters = () => {
    const resetFilters: AnalyticsFilter = {
      courses: [],
      sections: [],
      yearLevels: [],
      attendanceStatus: [],
      riskLevels: [],
      dateRange: {
        period: 'weekly',
        startDate: undefined,
        endDate: undefined
      }
    }
    setTempFilters(resetFilters)
  }

  const getActiveFiltersCount = () => {
    return (
      filters.courses.length +
      filters.sections.length +
      filters.yearLevels.length +
      filters.attendanceStatus.length +
      filters.riskLevels.length +
      (filters.dateRange.customRange ? 1 : 0)
    )
  }

  const handleCourseChange = (course: string, checked: boolean) => {
    setTempFilters(prev => ({
      ...prev,
      courses: checked 
        ? [...prev.courses, course]
        : prev.courses.filter(c => c !== course)
    }))
  }

  const handleSectionChange = (section: string, checked: boolean) => {
    setTempFilters(prev => ({
      ...prev,
      sections: checked 
        ? [...prev.sections, section]
        : prev.sections.filter(s => s !== section)
    }))
  }

  const handleYearLevelChange = (yearLevel: number, checked: boolean) => {
    setTempFilters(prev => ({
      ...prev,
      yearLevels: checked 
        ? [...prev.yearLevels, yearLevel]
        : prev.yearLevels.filter(y => y !== yearLevel)
    }))
  }

  const handleStatusChange = (status: AttendanceStatus, checked: boolean) => {
    setTempFilters(prev => ({
      ...prev,
      attendanceStatus: checked 
        ? [...prev.attendanceStatus, status]
        : prev.attendanceStatus.filter(s => s !== status)
    }))
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="relative">
          <Filter className="h-4 w-4 mr-2" />
          Filters
          {getActiveFiltersCount() > 0 && (
            <Badge 
              variant="destructive" 
              className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
            >
              {getActiveFiltersCount()}
            </Badge>
          )}
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Filter Analytics Data</DialogTitle>
          <DialogDescription>
            Customize your analytics view by applying filters
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Date Range Filter */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Date Range</Label>
            <DateRangePicker
              dateRange={tempFilters.dateRange.customRange}
              onDateRangeChange={(range) =>
                setTempFilters(prev => ({
                  ...prev,
                  dateRange: {
                    ...prev.dateRange,
                    customRange: range
                  }
                }))
              }
              placeholder="Select custom date range"
              className="w-full"
            />
          </div>

          {/* Course Filter */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Courses</Label>
            <div className="grid grid-cols-3 gap-3">
              {availableCourses.map((course) => (
                <div key={course} className="flex items-center space-x-2">
                  <Checkbox
                    id={`course-${course}`}
                    checked={tempFilters.courses.includes(course)}
                    onCheckedChange={(checked) => 
                      handleCourseChange(course, checked as boolean)
                    }
                  />
                  <Label htmlFor={`course-${course}`} className="text-sm">
                    {course}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          {/* Year Level Filter */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Year Levels</Label>
            <div className="grid grid-cols-4 gap-3">
              {availableYearLevels.map((year) => (
                <div key={year} className="flex items-center space-x-2">
                  <Checkbox
                    id={`year-${year}`}
                    checked={tempFilters.yearLevels.includes(year)}
                    onCheckedChange={(checked) => 
                      handleYearLevelChange(year, checked as boolean)
                    }
                  />
                  <Label htmlFor={`year-${year}`} className="text-sm">
                    Year {year}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          {/* Attendance Status Filter */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Attendance Status</Label>
            <div className="grid grid-cols-2 gap-3">
              {availableStatuses.map((status) => (
                <div key={status} className="flex items-center space-x-2">
                  <Checkbox
                    id={`status-${status}`}
                    checked={tempFilters.attendanceStatus.includes(status)}
                    onCheckedChange={(checked) => 
                      handleStatusChange(status, checked as boolean)
                    }
                  />
                  <Label htmlFor={`status-${status}`} className="text-sm capitalize">
                    {status}
                  </Label>
                </div>
              ))}
            </div>
          </div>
        </div>

        <DialogFooter className="flex justify-between">
          <Button variant="outline" onClick={handleResetFilters}>
            Reset All
          </Button>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => setOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleApplyFilters}>
              Apply Filters
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
