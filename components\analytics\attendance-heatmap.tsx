"use client"

import { Card } from "@/components/ui/card"

// Mock heatmap data - in a real app, this would come from props
const mockHeatmapData = [
  { day: 'Monday', hours: [45, 89, 67, 34, 23, 12, 8, 5] },
  { day: 'Tuesday', hours: [52, 94, 71, 38, 25, 15, 9, 6] },
  { day: 'Wednesday', hours: [48, 91, 73, 41, 28, 18, 11, 7] },
  { day: 'Thursday', hours: [50, 88, 69, 36, 24, 14, 8, 5] },
  { day: 'Friday', hours: [42, 82, 58, 29, 19, 10, 6, 4] },
  { day: 'Saturday', hours: [15, 25, 18, 8, 5, 2, 1, 0] },
  { day: 'Sunday', hours: [0, 0, 0, 0, 0, 0, 0, 0] }
]

const timeLabels = ['7AM', '8AM', '9AM', '10AM', '11AM', '12PM', '1PM', '2PM']

export function AttendanceHeatmap() {
  const getIntensityColor = (value: number) => {
    const maxValue = 100
    const intensity = value / maxValue
    
    if (intensity === 0) return 'bg-gray-100 dark:bg-gray-800'
    if (intensity <= 0.2) return 'bg-blue-100 dark:bg-blue-900'
    if (intensity <= 0.4) return 'bg-blue-200 dark:bg-blue-800'
    if (intensity <= 0.6) return 'bg-blue-400 dark:bg-blue-600'
    if (intensity <= 0.8) return 'bg-blue-500 dark:bg-blue-500'
    return 'bg-blue-600 dark:bg-blue-400'
  }

  const getTextColor = (value: number) => {
    const maxValue = 100
    const intensity = value / maxValue
    return intensity > 0.5 ? 'text-white' : 'text-gray-700 dark:text-gray-300'
  }

  return (
    <div className="space-y-4">
      {/* Legend */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
          <span>Less</span>
          <div className="flex gap-1">
            <div className="w-3 h-3 bg-gray-100 dark:bg-gray-800 rounded-sm"></div>
            <div className="w-3 h-3 bg-blue-100 dark:bg-blue-900 rounded-sm"></div>
            <div className="w-3 h-3 bg-blue-200 dark:bg-blue-800 rounded-sm"></div>
            <div className="w-3 h-3 bg-blue-400 dark:bg-blue-600 rounded-sm"></div>
            <div className="w-3 h-3 bg-blue-500 dark:bg-blue-500 rounded-sm"></div>
            <div className="w-3 h-3 bg-blue-600 dark:bg-blue-400 rounded-sm"></div>
          </div>
          <span>More</span>
        </div>
        <div className="text-sm text-gray-600 dark:text-gray-400">
          Check-ins per hour
        </div>
      </div>

      {/* Heatmap Grid */}
      <div className="overflow-x-auto">
        <div className="min-w-[600px]">
          {/* Time Headers */}
          <div className="grid grid-cols-9 gap-1 mb-2">
            <div className="text-xs text-gray-500 text-right pr-2"></div>
            {timeLabels.map((time) => (
              <div key={time} className="text-xs text-gray-500 text-center">
                {time}
              </div>
            ))}
          </div>

          {/* Heatmap Rows */}
          {mockHeatmapData.map((dayData) => (
            <div key={dayData.day} className="grid grid-cols-9 gap-1 mb-1">
              <div className="text-xs text-gray-500 text-right pr-2 py-2">
                {dayData.day.slice(0, 3)}
              </div>
              {dayData.hours.map((value, hourIndex) => (
                <Card
                  key={hourIndex}
                  className={`
                    h-8 flex items-center justify-center cursor-pointer transition-all hover:scale-105
                    ${getIntensityColor(value)} ${getTextColor(value)}
                  `}
                  title={`${dayData.day} ${timeLabels[hourIndex]}: ${value} check-ins`}
                >
                  <span className="text-xs font-medium">
                    {value > 0 ? value : ''}
                  </span>
                </Card>
              ))}
            </div>
          ))}
        </div>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-3 gap-4 mt-6">
        <Card className="p-3">
          <div className="text-center">
            <div className="text-lg font-bold text-blue-600">8:00 AM</div>
            <div className="text-xs text-gray-500">Peak Hour</div>
          </div>
        </Card>
        <Card className="p-3">
          <div className="text-center">
            <div className="text-lg font-bold text-green-600">Wednesday</div>
            <div className="text-xs text-gray-500">Best Day</div>
          </div>
        </Card>
        <Card className="p-3">
          <div className="text-center">
            <div className="text-lg font-bold text-purple-600">89.2%</div>
            <div className="text-xs text-gray-500">Avg Rate</div>
          </div>
        </Card>
      </div>
    </div>
  )
}
