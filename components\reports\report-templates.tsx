"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  Plus, 
  Search, 
  Filter,
  FileText,
  FileCheck,
  BarChart3,
  MessageSquare,
  Shield,
  Edit,
  Copy,
  Trash2,
  Download,
  Upload,
  CheckCircle,
  AlertCircle,
  XCircle,
  Star,
  Eye,
  Settings
} from "lucide-react"
import { PhilippineReportTemplate, PhilippineReportType } from "@/types"

interface ReportTemplatesProps {
  templates: PhilippineReportTemplate[]
  onCreateTemplate: () => void
  onEditTemplate: (templateId: string) => void
  onDuplicateTemplate: (templateId: string) => void
  onDeleteTemplate: (templateId: string) => void
  onPreviewTemplate: (templateId: string) => void
  onExportTemplate: (templateId: string) => void
  onImportTemplate: () => void
  isLoading?: boolean
}

// Mock data for demonstration
const mockTemplates: PhilippineReportTemplate[] = [
  {
    id: "1",
    type: "SF2",
    name: "Standard SF2 Daily Attendance",
    description: "Official DepEd SF2 form for daily attendance reporting",
    depedCode: "SF2",
    isOfficial: true,
    requiredFields: ["date", "grade", "section", "malePresent", "femalePresent"],
    optionalFields: ["remarks", "weather"],
    complianceLevel: "full",
    digitalSignatureRequired: true,
    template: {} as any,
    createdAt: new Date("2024-01-15"),
    updatedAt: new Date("2024-12-01")
  },
  {
    id: "2",
    type: "SF4",
    name: "Standard SF4 School Register",
    description: "Official DepEd SF4 school register template",
    depedCode: "SF4",
    isOfficial: true,
    requiredFields: ["lrn", "lastName", "firstName", "sex", "birthDate"],
    optionalFields: ["middleName", "religion", "address"],
    complianceLevel: "full",
    digitalSignatureRequired: true,
    template: {} as any,
    createdAt: new Date("2024-01-15"),
    updatedAt: new Date("2024-11-20")
  },
  {
    id: "3",
    type: "custom_attendance",
    name: "Weekly Attendance Summary",
    description: "Custom weekly attendance report with charts",
    isOfficial: false,
    requiredFields: ["studentName", "grade", "attendanceRate"],
    optionalFields: ["parentContact", "notes"],
    complianceLevel: "custom",
    digitalSignatureRequired: false,
    template: {} as any,
    createdAt: new Date("2024-02-10"),
    updatedAt: new Date("2024-12-10")
  },
  {
    id: "4",
    type: "sms_notifications",
    name: "SMS Delivery Report",
    description: "Report on SMS notification delivery status",
    isOfficial: false,
    requiredFields: ["recipient", "message", "status"],
    optionalFields: ["deliveryTime", "errorMessage"],
    complianceLevel: "custom",
    digitalSignatureRequired: false,
    template: {} as any,
    createdAt: new Date("2024-03-05"),
    updatedAt: new Date("2024-12-05")
  }
]

export function ReportTemplates({ 
  templates = mockTemplates,
  onCreateTemplate,
  onEditTemplate,
  onDuplicateTemplate,
  onDeleteTemplate,
  onPreviewTemplate,
  onExportTemplate,
  onImportTemplate,
  isLoading = false 
}: ReportTemplatesProps) {
  const [activeTab, setActiveTab] = useState("all")
  const [searchTerm, setSearchTerm] = useState("")
  const [typeFilter, setTypeFilter] = useState<PhilippineReportType | "all">("all")
  const [complianceFilter, setComplianceFilter] = useState<"all" | "full" | "partial" | "custom">("all")

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = typeFilter === "all" || template.type === typeFilter
    const matchesCompliance = complianceFilter === "all" || template.complianceLevel === complianceFilter
    const matchesTab = activeTab === "all" || 
                      (activeTab === "official" && template.isOfficial) ||
                      (activeTab === "custom" && !template.isOfficial)
    
    return matchesSearch && matchesType && matchesCompliance && matchesTab
  })

  const getTypeIcon = (type: PhilippineReportType) => {
    switch (type) {
      case "SF2":
      case "SF4":
        return <FileCheck className="h-4 w-4 text-blue-600" />
      case "custom_attendance":
        return <BarChart3 className="h-4 w-4 text-green-600" />
      case "sms_notifications":
        return <MessageSquare className="h-4 w-4 text-purple-600" />
      case "deped_compliance":
        return <Shield className="h-4 w-4 text-orange-600" />
      default:
        return <FileText className="h-4 w-4 text-gray-600" />
    }
  }

  const getComplianceBadge = (level: "full" | "partial" | "custom") => {
    const variants = {
      full: { variant: "default" as const, color: "text-green-600", icon: CheckCircle },
      partial: { variant: "secondary" as const, color: "text-yellow-600", icon: AlertCircle },
      custom: { variant: "outline" as const, color: "text-blue-600", icon: Settings }
    }

    const config = variants[level]
    const Icon = config.icon

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className={`h-3 w-3 ${config.color}`} />
        {level === "full" ? "Full DepEd" : level === "partial" ? "Partial" : "Custom"}
      </Badge>
    )
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    }).format(date)
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Report Templates
              </CardTitle>
              <CardDescription>
                Manage and customize report templates for Philippine education forms
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" onClick={onImportTemplate}>
                <Upload className="h-4 w-4 mr-2" />
                Import
              </Button>
              <Button onClick={onCreateTemplate}>
                <Plus className="h-4 w-4 mr-2" />
                New Template
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Filters */}
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search templates..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select value={typeFilter} onValueChange={(value) => setTypeFilter(value as PhilippineReportType | "all")}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="SF2">SF2</SelectItem>
                  <SelectItem value="SF4">SF4</SelectItem>
                  <SelectItem value="custom_attendance">Custom</SelectItem>
                  <SelectItem value="sms_notifications">SMS</SelectItem>
                  <SelectItem value="deped_compliance">Compliance</SelectItem>
                </SelectContent>
              </Select>
              <Select value={complianceFilter} onValueChange={(value) => setComplianceFilter(value as any)}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Compliance" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Compliance</SelectItem>
                  <SelectItem value="full">Full DepEd</SelectItem>
                  <SelectItem value="partial">Partial</SelectItem>
                  <SelectItem value="custom">Custom</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Tabs */}
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList>
                <TabsTrigger value="all">All Templates</TabsTrigger>
                <TabsTrigger value="official">Official DepEd</TabsTrigger>
                <TabsTrigger value="custom">Custom Templates</TabsTrigger>
              </TabsList>

              <TabsContent value={activeTab} className="space-y-4">
                {isLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600" />
                    <span className="ml-2">Loading templates...</span>
                  </div>
                ) : filteredTemplates.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                    <h3 className="text-lg font-medium mb-2">No templates found</h3>
                    <p className="text-sm">Try adjusting your filters or create a new template</p>
                  </div>
                ) : (
                  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    {filteredTemplates.map((template) => (
                      <Card key={template.id} className="hover:shadow-md transition-shadow">
                        <CardHeader className="pb-3">
                          <div className="flex items-start justify-between">
                            <div className="flex items-center gap-2">
                              {getTypeIcon(template.type)}
                              <div>
                                <h3 className="font-medium text-sm leading-tight">
                                  {template.name}
                                </h3>
                                {template.depedCode && (
                                  <Badge variant="secondary" className="text-xs mt-1">
                                    {template.depedCode}
                                  </Badge>
                                )}
                              </div>
                            </div>
                            <div className="flex flex-col gap-1">
                              {template.isOfficial && (
                                <Badge variant="outline" className="text-xs">
                                  <Star className="h-3 w-3 mr-1" />
                                  Official
                                </Badge>
                              )}
                              {getComplianceBadge(template.complianceLevel)}
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent className="space-y-3">
                          <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                            {template.description}
                          </p>

                          <div className="space-y-2">
                            <div className="flex items-center justify-between text-xs text-gray-500">
                              <span>Required Fields:</span>
                              <span>{template.requiredFields.length}</span>
                            </div>
                            <div className="flex items-center justify-between text-xs text-gray-500">
                              <span>Optional Fields:</span>
                              <span>{template.optionalFields.length}</span>
                            </div>
                            <div className="flex items-center justify-between text-xs text-gray-500">
                              <span>Digital Signature:</span>
                              <span>
                                {template.digitalSignatureRequired ? (
                                  <CheckCircle className="h-3 w-3 text-green-600" />
                                ) : (
                                  <XCircle className="h-3 w-3 text-gray-400" />
                                )}
                              </span>
                            </div>
                          </div>

                          <div className="text-xs text-gray-500 pt-2 border-t">
                            <div>Created: {formatDate(template.createdAt)}</div>
                            <div>Updated: {formatDate(template.updatedAt)}</div>
                          </div>

                          <div className="flex gap-1 pt-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => onPreviewTemplate(template.id)}
                              className="flex-1"
                            >
                              <Eye className="h-3 w-3 mr-1" />
                              Preview
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => onEditTemplate(template.id)}
                            >
                              <Edit className="h-3 w-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => onDuplicateTemplate(template.id)}
                            >
                              <Copy className="h-3 w-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => onExportTemplate(template.id)}
                            >
                              <Download className="h-3 w-3" />
                            </Button>
                            {!template.isOfficial && (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => onDeleteTemplate(template.id)}
                                className="text-red-600 hover:text-red-700"
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </div>
        </CardContent>
      </Card>

      {/* Template Statistics */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Total Templates</p>
                <p className="text-2xl font-bold">{templates.length}</p>
              </div>
              <FileText className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Official DepEd</p>
                <p className="text-2xl font-bold">
                  {templates.filter(t => t.isOfficial).length}
                </p>
              </div>
              <FileCheck className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Custom Templates</p>
                <p className="text-2xl font-bold">
                  {templates.filter(t => !t.isOfficial).length}
                </p>
              </div>
              <Settings className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Full Compliance</p>
                <p className="text-2xl font-bold">
                  {templates.filter(t => t.complianceLevel === "full").length}
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
