"use client"

import { useState } from "react"
import { MainLayout } from "@/components/layout/main-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import {
  FileText,
  Download,
  Calendar,
  Settings,
  BarChart3,
  Users,
  Clock,
  Shield,
  Plus,
  Eye
} from "lucide-react"
import { ReportTypeSelector } from "@/components/reports/report-type-selector"
import { ReportParametersForm } from "@/components/reports/report-parameters-form"
import { ReportPreview } from "@/components/reports/report-preview"
import { GeneratedReportsTable } from "@/components/reports/generated-reports-table"
import { ReportScheduling } from "@/components/reports/report-scheduling"
import { ReportTemplates } from "@/components/reports/report-templates"
import { BatchGeneration } from "@/components/reports/batch-generation"
import { PhilippineReportType, ReportFormData } from "@/types"

export default function ReportsPage() {
  const [activeTab, setActiveTab] = useState("generate")
  const [selectedReportType, setSelectedReportType] = useState<PhilippineReportType | undefined>()
  const [showPreview, setShowPreview] = useState(false)
  const [previewData, setPreviewData] = useState<ReportFormData | null>(null)
  const [isGenerating, setIsGenerating] = useState(false)

  const handleReportTypeSelect = (type: PhilippineReportType) => {
    setSelectedReportType(type)
    setShowPreview(false)
  }

  const handlePreviewReport = (data: ReportFormData) => {
    setPreviewData(data)
    setShowPreview(true)
  }

  const handleGenerateReport = (data: ReportFormData) => {
    setIsGenerating(true)
    // Simulate report generation
    setTimeout(() => {
      setIsGenerating(false)
      setShowPreview(false)
      setActiveTab("history")
    }, 3000)
  }

  const handleClosePreview = () => {
    setShowPreview(false)
    setPreviewData(null)
  }

  // Mock handlers for other components
  const handleDownloadReport = (reportId: string) => {
    console.log("Download report:", reportId)
  }

  const handlePreviewExistingReport = (reportId: string) => {
    console.log("Preview report:", reportId)
  }

  const handleShareReport = (reportId: string) => {
    console.log("Share report:", reportId)
  }

  const handleDeleteReport = (reportId: string) => {
    console.log("Delete report:", reportId)
  }

  const handleArchiveReport = (reportId: string) => {
    console.log("Archive report:", reportId)
  }

  const handleCreateSchedule = () => {
    console.log("Create schedule")
  }

  const handleSaveSchedule = (data: any) => {
    console.log("Save schedule:", data)
  }

  const handleCancelSchedule = () => {
    console.log("Cancel schedule")
  }

  const handleCreateTemplate = () => {
    console.log("Create template")
  }

  const handleEditTemplate = (templateId: string) => {
    console.log("Edit template:", templateId)
  }

  const handleDuplicateTemplate = (templateId: string) => {
    console.log("Duplicate template:", templateId)
  }

  const handleDeleteTemplate = (templateId: string) => {
    console.log("Delete template:", templateId)
  }

  const handlePreviewTemplate = (templateId: string) => {
    console.log("Preview template:", templateId)
  }

  const handleExportTemplate = (templateId: string) => {
    console.log("Export template:", templateId)
  }

  const handleImportTemplate = () => {
    console.log("Import template")
  }

  const handleCreateBatch = () => {
    console.log("Create batch")
  }

  const handleStartBatch = (batchId: string) => {
    console.log("Start batch:", batchId)
  }

  const handlePauseBatch = (batchId: string) => {
    console.log("Pause batch:", batchId)
  }

  const handleCancelBatch = (batchId: string) => {
    console.log("Cancel batch:", batchId)
  }

  const handleDeleteBatch = (batchId: string) => {
    console.log("Delete batch:", batchId)
  }

  const handleViewBatchResults = (batchId: string) => {
    console.log("View batch results:", batchId)
  }

  return (
    <MainLayout title="Philippine Education Reports">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              Philippine Education Reports
            </h2>
            <p className="text-gray-600 dark:text-gray-400">
              Generate official DepEd forms (SF2, SF4) and custom attendance reports
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={() => setActiveTab("scheduling")}>
              <Calendar className="h-4 w-4 mr-2" />
              Schedule Reports
            </Button>
            <Button variant="outline" size="sm" onClick={() => setActiveTab("batch")}>
              <Settings className="h-4 w-4 mr-2" />
              Batch Generation
            </Button>
            <Button size="sm" onClick={() => setActiveTab("generate")}>
              <Plus className="h-4 w-4 mr-2" />
              New Report
            </Button>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Reports</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">156</div>
              <p className="text-xs text-muted-foreground">
                Generated this month
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">DepEd Compliance</CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">98%</div>
              <p className="text-xs text-muted-foreground">
                Compliance rate
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Scheduled</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">12</div>
              <p className="text-xs text-muted-foreground">
                Automated reports
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Digital Signatures</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">45</div>
              <p className="text-xs text-muted-foreground">
                Signed documents
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="generate" className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Generate
            </TabsTrigger>
            <TabsTrigger value="history" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              History
            </TabsTrigger>
            <TabsTrigger value="templates" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Templates
            </TabsTrigger>
            <TabsTrigger value="scheduling" className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              Scheduling
            </TabsTrigger>
            <TabsTrigger value="batch" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Batch
            </TabsTrigger>
            <TabsTrigger value="analytics" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Analytics
            </TabsTrigger>
          </TabsList>

          <TabsContent value="generate" className="space-y-6">
            {!showPreview ? (
              <div className="space-y-6">
                <ReportTypeSelector
                  selectedType={selectedReportType}
                  onTypeSelect={handleReportTypeSelect}
                />

                {selectedReportType && (
                  <ReportParametersForm
                    reportType={selectedReportType}
                    onSubmit={handleGenerateReport}
                    onPreview={handlePreviewReport}
                    isLoading={isGenerating}
                  />
                )}
              </div>
            ) : (
              previewData && (
                <ReportPreview
                  reportData={previewData}
                  isGenerating={isGenerating}
                  onGenerate={() => handleGenerateReport(previewData)}
                  onClose={handleClosePreview}
                />
              )
            )}
          </TabsContent>

          <TabsContent value="history" className="space-y-6">
            <GeneratedReportsTable
              reports={[]}
              onDownload={handleDownloadReport}
              onPreview={handlePreviewExistingReport}
              onShare={handleShareReport}
              onDelete={handleDeleteReport}
              onArchive={handleArchiveReport}
            />
          </TabsContent>

          <TabsContent value="templates" className="space-y-6">
            <ReportTemplates
              templates={[]}
              onCreateTemplate={handleCreateTemplate}
              onEditTemplate={handleEditTemplate}
              onDuplicateTemplate={handleDuplicateTemplate}
              onDeleteTemplate={handleDeleteTemplate}
              onPreviewTemplate={handlePreviewTemplate}
              onExportTemplate={handleExportTemplate}
              onImportTemplate={handleImportTemplate}
            />
          </TabsContent>

          <TabsContent value="scheduling" className="space-y-6">
            <ReportScheduling
              onSave={handleSaveSchedule}
              onCancel={handleCancelSchedule}
            />
          </TabsContent>

          <TabsContent value="batch" className="space-y-6">
            <BatchGeneration
              batches={[]}
              onCreateBatch={handleCreateBatch}
              onStartBatch={handleStartBatch}
              onPauseBatch={handlePauseBatch}
              onCancelBatch={handleCancelBatch}
              onDeleteBatch={handleDeleteBatch}
              onViewResults={handleViewBatchResults}
            />
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Report Analytics
                </CardTitle>
                <CardDescription>
                  Insights and statistics about your report generation
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm text-gray-600 dark:text-gray-400">Most Generated</p>
                          <p className="text-lg font-bold">SF2 Reports</p>
                          <p className="text-xs text-gray-500">45% of all reports</p>
                        </div>
                        <FileText className="h-8 w-8 text-blue-600" />
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm text-gray-600 dark:text-gray-400">Avg. Generation Time</p>
                          <p className="text-lg font-bold">2.3 min</p>
                          <p className="text-xs text-gray-500">15% faster this month</p>
                        </div>
                        <Clock className="h-8 w-8 text-green-600" />
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm text-gray-600 dark:text-gray-400">Success Rate</p>
                          <p className="text-lg font-bold">99.2%</p>
                          <p className="text-xs text-gray-500">Excellent reliability</p>
                        </div>
                        <Shield className="h-8 w-8 text-orange-600" />
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-950 rounded-lg">
                  <div className="flex items-start gap-3">
                    <Eye className="h-5 w-5 text-blue-600 mt-0.5" />
                    <div className="space-y-1">
                      <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200">
                        Report Generation Insights
                      </h4>
                      <div className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                        <p>• Peak generation time: 9:00 AM - 11:00 AM</p>
                        <p>• Most popular format: PDF (78%)</p>
                        <p>• Average file size: 2.1 MB</p>
                        <p>• Digital signature usage: 85% for official reports</p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  )
}
