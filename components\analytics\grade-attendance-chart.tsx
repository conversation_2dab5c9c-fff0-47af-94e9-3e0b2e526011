"use client"

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON>lt<PERSON>, Responsive<PERSON><PERSON><PERSON>, <PERSON> } from 'recharts'
import type { GradeAttendanceData } from '@/types'

interface GradeAttendanceChartProps {
  data: GradeAttendanceData[]
}

export function GradeAttendanceChart({ data }: GradeAttendanceChartProps) {
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      return (
        <div className="bg-white dark:bg-gray-800 p-3 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg">
          <p className="font-medium">{label}</p>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Total Students: {data.totalStudents}
          </p>
          <p className="text-sm text-green-600">
            Present: {data.presentStudents} ({data.attendanceRate}%)
          </p>
          <p className="text-sm text-red-600">
            Absent: {data.absentStudents}
          </p>
          <p className="text-sm text-yellow-600">
            Late: {data.lateStudents}
          </p>
          {data.trend && (
            <p className={`text-sm ${data.trend > 0 ? 'text-green-600' : 'text-red-600'}`}>
              Trend: {data.trend > 0 ? '+' : ''}{data.trend}%
            </p>
          )}
        </div>
      )
    }
    return null
  }

  return (
    <div className="h-[300px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" className="stroke-gray-200 dark:stroke-gray-700" />
          <XAxis 
            dataKey="grade" 
            className="text-xs fill-gray-600 dark:fill-gray-400"
          />
          <YAxis className="text-xs fill-gray-600 dark:fill-gray-400" />
          <Tooltip content={<CustomTooltip />} />
          <Legend />
          <Bar 
            dataKey="presentStudents" 
            fill="#22c55e" 
            name="Present Students"
            radius={[2, 2, 0, 0]}
          />
          <Bar 
            dataKey="absentStudents" 
            fill="#ef4444" 
            name="Absent Students"
            radius={[2, 2, 0, 0]}
          />
          <Bar 
            dataKey="lateStudents" 
            fill="#f59e0b" 
            name="Late Students"
            radius={[2, 2, 0, 0]}
          />
        </BarChart>
      </ResponsiveContainer>
    </div>
  )
}
