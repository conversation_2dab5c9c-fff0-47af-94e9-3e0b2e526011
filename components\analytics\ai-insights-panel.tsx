"use client"

import { <PERSON><PERSON> } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Brain, TrendingUp, AlertTriangle, Lightbulb, Eye } from "lucide-react"
import type { AIInsight } from "@/types"

interface AIInsightsPanelProps {
  insights: AIInsight[]
}

export function AIInsightsPanel({ insights }: AIInsightsPanelProps) {
  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'prediction':
        return <TrendingUp className="h-4 w-4" />
      case 'pattern':
        return <Eye className="h-4 w-4" />
      case 'recommendation':
        return <Lightbulb className="h-4 w-4" />
      case 'alert':
        return <AlertTriangle className="h-4 w-4" />
      default:
        return <Brain className="h-4 w-4" />
    }
  }

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high':
        return 'destructive'
      case 'medium':
        return 'default'
      case 'low':
        return 'secondary'
      default:
        return 'default'
    }
  }

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 90) return 'text-green-600'
    if (confidence >= 70) return 'text-yellow-600'
    return 'text-red-600'
  }

  return (
    <div className="space-y-4">
      {insights.map((insight) => (
        <Card key={insight.id} className="hover:shadow-md transition-shadow">
          <CardHeader className="pb-3">
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-2">
                {getInsightIcon(insight.type)}
                <CardTitle className="text-base">{insight.title}</CardTitle>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant={getImpactColor(insight.impact)} className="text-xs">
                  {insight.impact} impact
                </Badge>
                <Badge variant="outline" className="text-xs">
                  <span className={getConfidenceColor(insight.confidence)}>
                    {insight.confidence}% confidence
                  </span>
                </Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <CardDescription className="text-sm mb-3">
              {insight.description}
            </CardDescription>
            
            {insight.actionable && insight.actions && insight.actions.length > 0 && (
              <div className="space-y-2">
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Recommended Actions:
                </p>
                <ul className="space-y-1">
                  {insight.actions.map((action, index) => (
                    <li key={index} className="text-sm text-gray-600 dark:text-gray-400 flex items-center gap-2">
                      <div className="w-1.5 h-1.5 bg-blue-500 rounded-full flex-shrink-0" />
                      {action}
                    </li>
                  ))}
                </ul>
                <div className="flex gap-2 mt-3">
                  <Button size="sm" variant="outline">
                    View Details
                  </Button>
                  <Button size="sm">
                    Take Action
                  </Button>
                </div>
              </div>
            )}
            
            <div className="flex items-center justify-between mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
              <Badge variant="outline" className="text-xs capitalize">
                {insight.category}
              </Badge>
              <span className="text-xs text-gray-500">
                {insight.createdAt.toLocaleDateString()}
              </span>
            </div>
          </CardContent>
        </Card>
      ))}
      
      {insights.length === 0 && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-8">
            <Brain className="h-12 w-12 text-gray-400 mb-4" />
            <p className="text-gray-500 text-center">
              No AI insights available at the moment.
              <br />
              Check back later for intelligent analysis and recommendations.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
